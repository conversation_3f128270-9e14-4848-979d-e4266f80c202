{"name": "zakmakelaar-ai", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "expo": "~53.0.17", "expo-background-fetch": "~13.1.6", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-device": "^7.1.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-notifications": "^0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-task-manager": "~13.1.6", "expo-web-browser": "~14.2.0", "i18next": "^23.15.2", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.1.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.6", "expo-dev-client": "~5.2.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^30.0.4", "jest-environment-node": "^30.0.4", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}
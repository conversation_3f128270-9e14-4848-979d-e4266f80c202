import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { User, UserPreferences, authService } from "../services/authService";

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  register: (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    phoneNumber?: string,
    options?: { isPropertyOwner?: boolean }
  ) => Promise<boolean>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  updatePreferences: (
    preferences: Partial<UserPreferences>
  ) => Promise<boolean>;
  updateProfile: (profileData: Partial<User>) => Promise<boolean>;
  updateUser: (userData: User) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  checkAuthStatus: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.login({ email, password });

          if (response.success && response.data) {
            set({
              user: response.data.user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({
              error: response.message || "Login failed",
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          set({
            error: error.message || "Login failed",
            isLoading: false,
          });
          return false;
        }
      },

      register: async (
        email: string,
        password: string,
        firstName?: string,
        lastName?: string,
        phoneNumber?: string,
        options?: { isPropertyOwner?: boolean }
      ) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.register({
            email,
            password,
            firstName,
            lastName,
            phoneNumber,
            isPropertyOwner: options?.isPropertyOwner || false,
          });

          if (response.success && response.data) {
            // Ensure user has an empty preferences object if it doesn't exist
            const userData = response.data.user;
            if (!userData.preferences) {
              userData.preferences = {
                maxPrice: 0,
                minPrice: 0,
              };
            }

            // WORKAROUND: Backend isn't setting the role correctly for property owners
            // If isPropertyOwner is true, manually update the user's role and property owner status
            if (options?.isPropertyOwner) {
              console.log(
                "Applying property owner workaround - updating user role"
              );
              try {
                // Update the user object locally
                userData.role = "owner";
                // Ensure propertyOwner conforms to the expected shape
                userData.propertyOwner = {
                  isPropertyOwner: true,
                  properties: userData.propertyOwner?.properties ?? [],
                  verificationStatus:
                    userData.propertyOwner?.verificationStatus ?? "unverified",
                };

                // Update the user in the store
                set({
                  user: userData,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                });

                console.log(
                  "Property owner workaround applied - user role updated to:",
                  userData.role
                );
                return true;
              } catch (updateError) {
                console.error(
                  "Failed to apply property owner workaround:",
                  updateError
                );
                // Continue with normal flow if workaround fails
              }
            }

            set({
              user: userData,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({
              error: response.message || "Registration failed",
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          set({
            error: error.message || "Registration failed",
            isLoading: false,
          });
          return false;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          await authService.logout();
        } catch (error) {
          console.warn("Logout error:", error);
        } finally {
          // Clear all auth-related state immediately
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          // Clear listings store if needed
          try {
            const { useListingsStore } = await import("./listingsStore");
            useListingsStore.getState().clearListings();
          } catch (error) {
            console.warn("Failed to clear listings store:", error);
          }

          // Immediately navigate to appropriate screen after logout
          try {
            const { welcomeService } = await import(
              "../services/welcomeService"
            );
            const hasSeenWelcome = await welcomeService.hasSeenWelcome();

            // Use dynamic import to avoid circular dependency
            const { router } = await import("expo-router");

            if (hasSeenWelcome) {
              console.log(
                "Logout - User has seen welcome screen, redirecting to login"
              );
              router.replace("/login");
            } else {
              console.log(
                "Logout - User has not seen welcome screen, redirecting to welcome"
              );
              router.replace("/");
            }
          } catch (navError) {
            console.error("Logout navigation error:", navError);
            // Fallback navigation
            try {
              const { router } = await import("expo-router");
              router.replace("/login");
            } catch (fallbackError) {
              console.error("Fallback navigation error:", fallbackError);
            }
          }

          // Don't clear welcome status on logout - let returning users skip welcome screen
          // This preserves the user experience for returning users
        }
      },

      getCurrentUser: async () => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.getCurrentUser();

          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: response.message || "Failed to get user data",
            });
          }
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || "Failed to get user data",
          });
        }
      },

      updatePreferences: async (preferences: Partial<UserPreferences>) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.updatePreferences(preferences);

          if (response.success && response.data) {
            // Extract user data from response
            // Handle both cases: when response.data is the user object directly or when it contains a user property
            const userData = (
              "user" in response.data ? response.data.user : response.data
            ) as User;

            // Ensure preferences are properly set in the user object
            if (!userData.preferences) {
              userData.preferences = preferences as UserPreferences;
            } else {
              userData.preferences = {
                ...userData.preferences,
                ...preferences,
              };
            }

            // Update the user in the store
            set({
              user: userData,
              isLoading: false,
            });

            // Log success for debugging
            console.log(
              "Preferences updated successfully:",
              userData.preferences
            );

            return true;
          } else {
            set({
              error: response.message || "Failed to update preferences",
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          console.error("Error updating preferences:", error);
          set({
            error: error.message || "Failed to update preferences",
            isLoading: false,
          });
          return false;
        }
      },

      updateProfile: async (profileData: Partial<User>) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.updateProfile(profileData);

          if (response.success && response.data) {
            set({
              user: response.data,
              isLoading: false,
            });
            return true;
          } else {
            set({
              error: response.message || "Failed to update profile",
              isLoading: false,
            });
            return false;
          }
        } catch (error: any) {
          set({
            error: error.message || "Failed to update profile",
            isLoading: false,
          });
          return false;
        }
      },

      updateUser: (userData: User) => {
        set({ user: userData });
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      checkAuthStatus: async () => {
        try {
          const token = await authService.getAuthToken();

          if (token) {
            // Validate token by calling /auth/me
            try {
              const userResult = await authService.getCurrentUser();
              if (userResult.success && userResult.data) {
                console.log("Auth Store - User authenticated on app load:", {
                  userId: userResult.data._id || userResult.data.id,
                  role: userResult.data.role,
                  isPropertyOwner:
                    userResult.data.propertyOwner?.isPropertyOwner,
                });

                set({
                  user: userResult.data,
                  isAuthenticated: true,
                });
                return;
              }
            } catch (error) {
              console.warn("Token validation failed:", error);
              // Token is invalid, clear it
              await authService.logout();
            }
          }

          // No token or invalid token
          console.log(
            "Auth Store - No valid token found, user not authenticated"
          );
          set({
            user: null,
            isAuthenticated: false,
          });
        } catch (error) {
          console.warn("Auth status check failed:", error);
          set({
            user: null,
            isAuthenticated: false,
          });
        }
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist user data and auth status, not loading/error states
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

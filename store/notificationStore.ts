import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { apiService } from "../services/api";

// Notification types
export interface AppNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  read: boolean;
  createdAt: Date;
  category: 'match' | 'application' | 'response' | 'insight' | 'system';
  priority: 'high' | 'medium' | 'low';
}

export interface LocalNotification {
  title: string;
  body: string;
  data?: any;
  trigger?: Notifications.NotificationTriggerInput;
}

export interface NotificationSettings {
  enabled: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string; // 24-hour format, e.g., "22:00"
  quietHoursEnd: string; // 24-hour format, e.g., "07:00"
  categories: {
    matches: boolean;
    applications: boolean;
    responses: boolean;
    insights: boolean;
    system: boolean;
  };
  frequency: 'immediate' | 'hourly' | 'daily';
  batchNotifications: boolean;
}

interface NotificationState {
  // State
  notifications: AppNotification[];
  unreadCount: number;
  settings: NotificationSettings;
  pushToken: string | null;
  permissionStatus: Notifications.PermissionStatus | null;
  isRegistered: boolean;
  
  // Actions
  initializeNotifications: () => Promise<boolean>;
  registerForPushNotifications: () => Promise<boolean>;
  updatePushToken: (token: string) => Promise<boolean>;
  handleNotification: (notification: Notifications.Notification) => void;
  addNotification: (notification: Omit<AppNotification, 'id' | 'createdAt' | 'read'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  scheduleLocalNotification: (notification: LocalNotification) => Promise<string | null>;
  cancelScheduledNotification: (notificationId: string) => Promise<boolean>;
  getScheduledNotifications: () => Promise<Notifications.NotificationRequest[]>;
  requestPermissions: () => Promise<boolean>;
  checkPermissions: () => Promise<Notifications.PermissionStatus>;
}

// Default notification settings
const defaultSettings: NotificationSettings = {
  enabled: true,
  pushEnabled: true,
  emailEnabled: true,
  smsEnabled: false,
  quietHoursEnabled: false,
  quietHoursStart: "22:00",
  quietHoursEnd: "07:00",
  categories: {
    matches: true,
    applications: true,
    responses: true,
    insights: true,
    system: true,
  },
  frequency: 'immediate',
  batchNotifications: false,
};

export const useNotificationStore = create<NotificationState>()(
  persist(
    (set, get) => ({
      // Initial state
      notifications: [],
      unreadCount: 0,
      settings: defaultSettings,
      pushToken: null,
      permissionStatus: null,
      isRegistered: false,
      
      // Initialize notifications
      initializeNotifications: async () => {
        try {
          // Configure notification behavior
          Notifications.setNotificationHandler({
            handleNotification: async () => ({
              shouldShowAlert: true,
              shouldPlaySound: true,
              shouldSetBadge: true,
            }),
          });
          
          // Add notification received listener
          const subscription = Notifications.addNotificationReceivedListener(
            (notification) => {
              get().handleNotification(notification);
            }
          );
          
          // Check permissions
          const status = await get().checkPermissions();
          
          // Request permissions if not granted
          if (status !== Notifications.PermissionStatus.GRANTED) {
            await get().requestPermissions();
          }
          
          // Register for push notifications if permissions granted
          if (status === Notifications.PermissionStatus.GRANTED) {
            await get().registerForPushNotifications();
          }
          
          return true;
        } catch (error) {
          console.error('Failed to initialize notifications:', error);
          return false;
        }
      },
      
      // Register for push notifications
      registerForPushNotifications: async () => {
        try {
          // Skip if already registered
          if (get().isRegistered && get().pushToken) {
            return true;
          }
          
          // Check if permission is granted
          const status = await get().checkPermissions();
          if (status !== Notifications.PermissionStatus.GRANTED) {
            return false;
          }
          
          // Get push token
          const { data: token } = await Notifications.getExpoPushTokenAsync({
            projectId: process.env.EXPO_PROJECT_ID,
          });
          
          // Update token in store
          await get().updatePushToken(token);
          
          // Update registration status
          set({ isRegistered: true });
          
          return true;
        } catch (error) {
          console.error('Failed to register for push notifications:', error);
          return false;
        }
      },
      
      // Update push token
      updatePushToken: async (token: string) => {
        try {
          // Update token in store
          set({ pushToken: token });
          
          // Send token to backend
          await apiService.post('/notifications/register', {
            token,
            platform: Platform.OS,
            deviceName: Platform.OS === 'ios' ? 'iOS Device' : 'Android Device',
          });
          
          return true;
        } catch (error) {
          console.error('Failed to update push token:', error);
          return false;
        }
      },
      
      // Handle received notification
      handleNotification: (notification: Notifications.Notification) => {
        const { title, body } = notification.request.content;
        const data = notification.request.content.data;
        
        // Determine notification category
        let category: AppNotification['category'] = 'system';
        if (data?.type) {
          if (data.type === 'new_matches') category = 'match';
          else if (data.type === 'application_status') category = 'application';
          else if (data.type === 'landlord_response') category = 'response';
          else if (data.type === 'market_insight') category = 'insight';
        }
        
        // Determine notification priority
        let priority: AppNotification['priority'] = 'medium';
        if (category === 'response') priority = 'high';
        else if (category === 'insight') priority = 'low';
        
        // Add notification to store
        get().addNotification({
          title: title || 'New Notification',
          body: body || '',
          data,
          category,
          priority,
        });
      },
      
      // Add notification to store
      addNotification: (notification) => {
        const newNotification: AppNotification = {
          id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
          read: false,
          ...notification,
        };
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1,
        }));
      },
      
      // Mark notification as read
      markAsRead: (notificationId: string) => {
        set((state) => {
          const updatedNotifications = state.notifications.map((notification) =>
            notification.id === notificationId
              ? { ...notification, read: true }
              : notification
          );
          
          const unreadCount = updatedNotifications.filter(
            (notification) => !notification.read
          ).length;
          
          return {
            notifications: updatedNotifications,
            unreadCount,
          };
        });
      },
      
      // Mark all notifications as read
      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map((notification) => ({
            ...notification,
            read: true,
          })),
          unreadCount: 0,
        }));
      },
      
      // Delete notification
      deleteNotification: (notificationId: string) => {
        set((state) => {
          const updatedNotifications = state.notifications.filter(
            (notification) => notification.id !== notificationId
          );
          
          const unreadCount = updatedNotifications.filter(
            (notification) => !notification.read
          ).length;
          
          return {
            notifications: updatedNotifications,
            unreadCount,
          };
        });
      },
      
      // Clear all notifications
      clearAllNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
        });
      },
      
      // Update notification settings
      updateSettings: (settings: Partial<NotificationSettings>) => {
        set((state) => ({
          settings: {
            ...state.settings,
            ...settings,
          },
        }));
      },
      
      // Schedule local notification
      scheduleLocalNotification: async (notification: LocalNotification) => {
        try {
          // Check if notifications are enabled
          if (!get().settings.enabled || !get().settings.pushEnabled) {
            return null;
          }
          
          // Check if in quiet hours
          if (get().settings.quietHoursEnabled) {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const currentTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
            
            const start = get().settings.quietHoursStart;
            const end = get().settings.quietHoursEnd;
            
            // Check if current time is within quiet hours
            if (
              (start <= end && currentTime >= start && currentTime <= end) ||
              (start > end && (currentTime >= start || currentTime <= end))
            ) {
              // Schedule for after quiet hours
              const [endHour, endMinute] = end.split(':').map(Number);
              const scheduledDate = new Date();
              
              if (currentTime <= end) {
                // Schedule for today's end of quiet hours
                scheduledDate.setHours(endHour, endMinute, 0);
              } else {
                // Schedule for tomorrow's end of quiet hours
                scheduledDate.setDate(scheduledDate.getDate() + 1);
                scheduledDate.setHours(endHour, endMinute, 0);
              }
              
              notification.trigger = scheduledDate;
            }
          }
          
          // Schedule notification
          const notificationId = await Notifications.scheduleNotificationAsync({
            content: {
              title: notification.title,
              body: notification.body,
              data: notification.data || {},
              sound: true,
              badge: get().unreadCount + 1,
            },
            trigger: notification.trigger || null,
          });
          
          return notificationId;
        } catch (error) {
          console.error('Failed to schedule local notification:', error);
          return null;
        }
      },
      
      // Cancel scheduled notification
      cancelScheduledNotification: async (notificationId: string) => {
        try {
          await Notifications.cancelScheduledNotificationAsync(notificationId);
          return true;
        } catch (error) {
          console.error('Failed to cancel scheduled notification:', error);
          return false;
        }
      },
      
      // Get scheduled notifications
      getScheduledNotifications: async () => {
        try {
          return await Notifications.getAllScheduledNotificationsAsync();
        } catch (error) {
          console.error('Failed to get scheduled notifications:', error);
          return [];
        }
      },
      
      // Request notification permissions
      requestPermissions: async () => {
        try {
          const { status } = await Notifications.requestPermissionsAsync({
            ios: {
              allowAlert: true,
              allowBadge: true,
              allowSound: true,
              allowAnnouncements: true,
            },
          });
          
          set({ permissionStatus: status });
          
          return status === Notifications.PermissionStatus.GRANTED;
        } catch (error) {
          console.error('Failed to request notification permissions:', error);
          return false;
        }
      },
      
      // Check notification permissions
      checkPermissions: async () => {
        try {
          const { status } = await Notifications.getPermissionsAsync();
          set({ permissionStatus: status });
          return status;
        } catch (error) {
          console.error('Failed to check notification permissions:', error);
          return Notifications.PermissionStatus.UNDETERMINED;
        }
      },
    }),
    {
      name: "notification-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        notifications: state.notifications,
        settings: state.settings,
        pushToken: state.pushToken,
        isRegistered: state.isRegistered,
      }),
    }
  )
);
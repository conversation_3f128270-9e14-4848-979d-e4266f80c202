import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import {
  aiService,
  ApplicationGenerationRequest,
  ApplicationGenerationResult,
  ContractAnalysisRequest,
  ContractAnalysisResult,
  MarketAnalysisRequest,
  MarketAnalysisResult,
  MatchingRequest,
  MatchingResult,
  TranslationRequest,
  TranslationResult,
} from "../services/aiService";
import { User, UserPreferences } from "../services/authService";
import { Listing } from "../services/listingsService";
import { autoApplicationService } from "../services/autoApplicationService";
import { useAuthStore } from "./authStore";

// Enhanced types for AI Store
export interface PropertyMatch extends MatchingResult {
  id: string;
  timestamp: Date | string; // Can be string after JSON serialization
  viewed: boolean;
  applied: boolean;
  saved: boolean;
}

export interface GeneratedApplication {
  id: string;
  listingId: string;
  listingUrl?: string;
  propertyTitle: string;
  content: ApplicationGenerationResult;
  status: "draft" | "approved" | "submitted" | "responded";
  submissionMethod: "manual" | "autonomous";
  createdAt: Date | string; // Can be string after JSON serialization
  submittedAt?: Date | string; // Can be string after JSON serialization
  response?: {
    status: "accepted" | "rejected" | "pending";
    message?: string;
    receivedAt: Date | string; // Can be string after JSON serialization
  };
}

export interface ContractAnalysis {
  id: string;
  contractName: string;
  analysis: ContractAnalysisResult;
  createdAt: Date | string; // Can be string after JSON serialization
  listingId?: string;
}

export interface MarketInsights {
  location: string;
  analysis: MarketAnalysisResult;
  lastUpdated: Date | string; // Can be string after JSON serialization
  userSpecific: boolean;
}

export interface AutonomousSettings {
  enabled: boolean;
  autoApplyThreshold: number; // 0-100
  maxApplicationsPerDay: number;
  maxApplicationsPerWeek: number;
  autoApplyPropertyTypes: string[];
  autoApplyMaxPrice: number;
  autoApplyMinMatchScore: number;
  defaultApplicationStyle: "professional" | "personal" | "creative";
  includePersonalTouch: boolean;
  requireConfirmationForExpensive: boolean;
  pauseOnMultipleRejections: boolean;
  maxBudgetOverride: number;
  notifyOnApplication: boolean;
  notifyOnResponse: boolean;
  dailySummary: boolean;
}

export interface AutonomousStatus {
  isActive: boolean;
  currentActivity?: string;
  applicationsToday: number;
  applicationsThisWeek: number;
  lastActivity?: Date | string; // Can be string after JSON serialization
  pausedReason?: string;
  nextScheduledCheck?: Date | string; // Can be string after JSON serialization
}

export interface AutonomousActivity {
  id: string;
  type:
    | "application_generated"
    | "application_submitted"
    | "match_found"
    | "error"
    | "paused"
    | "resumed";
  listingId?: string;
  propertyTitle?: string;
  message: string;
  timestamp: Date | string; // Can be string after JSON serialization
  success: boolean;
  details?: any;
}

interface AIState {
  // Property matching state
  matches: PropertyMatch[];
  matchingInProgress: boolean;
  lastMatchUpdate: Date | string | null; // Can be string after JSON serialization
  matchingError: string | null;

  // Applications state
  applications: GeneratedApplication[];
  applicationInProgress: boolean;
  applicationError: string | null;

  // Contract analysis state
  contractAnalyses: ContractAnalysis[];
  analysisInProgress: boolean;
  analysisError: string | null;

  // Market insights state
  marketInsights: MarketInsights[];
  insightsInProgress: boolean;
  insightsError: string | null;

  // Autonomous mode state
  autonomousSettings: AutonomousSettings;
  autonomousStatus: AutonomousStatus;
  autonomousActivities: AutonomousActivity[];
  lastStatusFetch: number; // Timestamp of last status fetch for throttling

  // Translation state
  translationCache: Map<string, TranslationResult>;
  translationInProgress: boolean;

  // General state
  isLoading: boolean;
  error: string | null;

  // Property matching actions
  requestPropertyMatching: (
    preferences: UserPreferences,
    user: User
  ) => Promise<PropertyMatch[]>;
  refreshMatches: () => Promise<void>;
  markMatchViewed: (matchId: string) => void;
  markMatchApplied: (matchId: string) => void;
  saveMatch: (matchId: string) => void;
  unsaveMatch: (matchId: string) => void;

  // Application generation actions
  generateApplication: (
    listing: Listing,
    user: User,
    style?: "professional" | "personal" | "creative" | "student" | "expat",
    customMessage?: string
  ) => Promise<GeneratedApplication>;
  approveApplication: (applicationId: string) => Promise<boolean>;
  submitApplication: (
    applicationId: string,
    method: "manual" | "autonomous"
  ) => Promise<boolean>;
  updateApplicationStatus: (
    applicationId: string,
    status: GeneratedApplication["status"]
  ) => void;
  deleteApplication: (applicationId: string) => void;

  // Contract analysis actions
  analyzeContract: (
    request: ContractAnalysisRequest,
    contractName: string,
    listingId?: string
  ) => Promise<ContractAnalysis>;
  deleteContractAnalysis: (analysisId: string) => void;

  // Market insights actions
  getMarketInsights: (
    location: string,
    preferences?: UserPreferences
  ) => Promise<MarketInsights>;
  refreshMarketInsights: (location: string) => Promise<void>;

  // Autonomous mode actions
  updateAutonomousSettings: (
    settings: Partial<AutonomousSettings>
  ) => Promise<boolean>;
  startAutonomousMode: () => Promise<boolean>;
  stopAutonomousMode: () => Promise<boolean>;
  pauseAutonomousMode: (reason: string) => Promise<boolean>;
  resumeAutonomousMode: () => Promise<boolean>;
  getAutonomousStatus: () => AutonomousStatus;
  fetchAutonomousStatus: (force?: boolean) => Promise<void>;
  fetchAutonomousActivities: () => Promise<void>;
  addAutonomousActivity: (
    activity: Omit<AutonomousActivity, "id" | "timestamp">
  ) => void;
  clearAutonomousActivities: () => void;

  // Safety control methods
  checkDailyLimit: () => boolean;
  checkWeeklyLimit: () => boolean;
  checkBudgetOverride: (
    propertyPrice: number,
    userMaxBudget: number
  ) => boolean;
  shouldRequireConfirmation: (
    propertyPrice: number,
    userMaxBudget: number
  ) => boolean;
  checkRecentRejections: () => boolean;
  enforceApplicationLimits: () => Promise<boolean>;
  resetDailyCounters: () => void;
  resetWeeklyCounters: () => void;

  // Translation actions
  translateText: (
    text: string,
    targetLanguage: "en" | "nl"
  ) => Promise<TranslationResult>;
  clearTranslationCache: () => void;

  // Utility actions
  clearError: () => void;
  clearAllData: () => void;
  getMatchById: (matchId: string) => PropertyMatch | undefined;
  getApplicationById: (
    applicationId: string
  ) => GeneratedApplication | undefined;
  getAnalysisById: (analysisId: string) => ContractAnalysis | undefined;
}

const defaultAutonomousSettings: AutonomousSettings = {
  enabled: false,
  autoApplyThreshold: 80,
  maxApplicationsPerDay: 5,
  maxApplicationsPerWeek: 20,
  autoApplyPropertyTypes: [],
  autoApplyMaxPrice: 2000,
  autoApplyMinMatchScore: 75,
  defaultApplicationStyle: "professional",
  includePersonalTouch: true,
  requireConfirmationForExpensive: true,
  pauseOnMultipleRejections: true,
  maxBudgetOverride: 10,
  notifyOnApplication: true,
  notifyOnResponse: true,
  dailySummary: true,
};

const defaultAutonomousStatus: AutonomousStatus = {
  isActive: false,
  applicationsToday: 0,
  applicationsThisWeek: 0,
};

export const useAIStore = create<AIState>()(
  persist(
    (set, get) => ({
      // Initial state
      matches: [],
      matchingInProgress: false,
      lastMatchUpdate: null,
      matchingError: null,

      applications: [],
      applicationInProgress: false,
      applicationError: null,

      contractAnalyses: [],
      analysisInProgress: false,
      analysisError: null,

      marketInsights: [],
      insightsInProgress: false,
      insightsError: null,

      autonomousSettings: defaultAutonomousSettings,
      autonomousStatus: defaultAutonomousStatus,
      autonomousActivities: [],
      lastStatusFetch: 0,

      translationCache: new Map(),
      translationInProgress: false,

      isLoading: false,
      error: null,

      // Property matching actions
      requestPropertyMatching: async (
        preferences: UserPreferences,
        user: User
      ) => {
        set({ matchingInProgress: true, matchingError: null });

        try {
          const request: MatchingRequest = {
            userProfile: user,
            preferences,
            maxResults: 50,
          };

          const response = await aiService.getPropertyMatches(request);

          if (response.success && response.data) {
            const newMatches: PropertyMatch[] = response.data.matches.map(
              (match, index) => ({
                ...match,
                id: `match_${Date.now()}_${index}`,
                timestamp: new Date(),
                viewed: false,
                applied: false,
                saved: false,
              })
            );

            set({
              matches: newMatches,
              lastMatchUpdate: new Date(),
              matchingInProgress: false,
              matchingError: null,
            });

            return newMatches;
          } else {
            const error = response.message || "Failed to get property matches";
            set({
              matchingError: error,
              matchingInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          // Handle "No suitable match found" as a valid empty result, not an error
          if (
            error.status === 404 &&
            error.message === "No suitable match found"
          ) {
            set({
              matches: [],
              lastMatchUpdate: new Date(),
              matchingInProgress: false,
              matchingError: null,
            });
            return [];
          }

          const errorMessage =
            error.message || "Failed to get property matches";
          set({
            matchingError: errorMessage,
            matchingInProgress: false,
          });
          throw error;
        }
      },

      refreshMatches: async () => {
        const state = get();
        if (state.lastMatchUpdate) {
          // Re-run matching with last known preferences
          // This would need to be implemented with stored preferences
          console.log("Refreshing matches...");
        }
      },

      markMatchViewed: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, viewed: true } : match
          ),
        }));
      },

      markMatchApplied: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, applied: true } : match
          ),
        }));
      },

      saveMatch: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, saved: true } : match
          ),
        }));
      },

      unsaveMatch: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, saved: false } : match
          ),
        }));
      },

      // Application generation actions
      generateApplication: async (
        listing: Listing,
        user: User,
        style:
          | "professional"
          | "personal"
          | "creative"
          | "student"
          | "expat" = "professional",
        customMessage?: string
      ) => {
        set({ applicationInProgress: true, applicationError: null });

        try {
          const request: ApplicationGenerationRequest = {
            listing,
            userProfile: user,
            template: style,
            customMessage,
            includeDocuments: true,
          };

          const response = await aiService.generateApplication(request);

          if (response.success && response.data) {
            const newApplication: GeneratedApplication = {
              id: `app_${Date.now()}`,
              listingId: listing._id,
              listingUrl: listing.url,
              propertyTitle: listing.title,
              content: response.data,
              status: "draft",
              submissionMethod: "manual",
              createdAt: new Date(),
            };

            set((state) => ({
              applications: [...state.applications, newApplication],
              applicationInProgress: false,
              applicationError: null,
            }));

            return newApplication;
          } else {
            const error = response.message || "Failed to generate application";
            set({
              applicationError: error,
              applicationInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage =
            error.message || "Failed to generate application";
          set({
            applicationError: errorMessage,
            applicationInProgress: false,
          });
          throw error;
        }
      },

      approveApplication: async (applicationId: string) => {
        set((state) => ({
          applications: state.applications.map((app) =>
            app.id === applicationId ? { ...app, status: "approved" } : app
          ),
        }));
        return true;
      },

      submitApplication: async (
        applicationId: string,
        method: "manual" | "autonomous"
      ) => {
        try {
          const state = get();
          const application = state.applications.find(
            (app) => app.id === applicationId
          );

          if (!application) {
            throw new Error("Application not found");
          }

          // Call the AI service to submit the application
          const response = await aiService.submitApplication(
            applicationId,
            method,
            {
              listingId: application.listingId,
              listingUrl:
                application.listingUrl ||
                `https://example.com/listing/${application.listingId}`,
              message: application.content.message,
              subject: application.content.subject,
              propertyTitle: application.propertyTitle,
            }
          );

          if (response.success && response.data) {
            // Update application status on successful submission
            set((state) => ({
              applications: state.applications.map((app) =>
                app.id === applicationId
                  ? {
                      ...app,
                      status: "submitted",
                      submissionMethod: method,
                      submittedAt: new Date(),
                      queueItemId: response.data?.submissionId || null, // Store submission ID for autonomous submissions
                    }
                  : app
              ),
            }));

            // Update autonomous status if autonomous submission
            if (method === "autonomous") {
              const currentState = get();
              set({
                autonomousStatus: {
                  ...currentState.autonomousStatus,
                  applicationsToday:
                    currentState.autonomousStatus.applicationsToday + 1,
                  applicationsThisWeek:
                    currentState.autonomousStatus.applicationsThisWeek + 1,
                  lastActivity: new Date(),
                },
              });
            }

            return true; // Return boolean for success
          } else {
            throw new Error(response.message || "Submission failed");
          }
        } catch (error) {
          console.error("Failed to submit application:", error);

          // Update application status to show error
          set((state) => ({
            applications: state.applications.map((app) =>
              app.id === applicationId
                ? {
                    ...app,
                    status: "draft", // Reset to draft on failure
                  }
                : app
            ),
            applicationError:
              error instanceof Error ? error.message : "Submission failed",
          }));

          return false; // Return boolean for failure
        }
      },

      updateApplicationStatus: (
        applicationId: string,
        status: GeneratedApplication["status"]
      ) => {
        set((state) => ({
          applications: state.applications.map((app) =>
            app.id === applicationId ? { ...app, status } : app
          ),
        }));
      },

      deleteApplication: (applicationId: string) => {
        set((state) => ({
          applications: state.applications.filter(
            (app) => app.id !== applicationId
          ),
        }));
      },

      // Contract analysis actions
      analyzeContract: async (
        request: ContractAnalysisRequest,
        contractName: string,
        listingId?: string
      ) => {
        set({ analysisInProgress: true, analysisError: null });

        try {
          const response = await aiService.analyzeContract(request);

          if (response.success && response.data) {
            const newAnalysis: ContractAnalysis = {
              id: `analysis_${Date.now()}`,
              contractName,
              analysis: response.data,
              createdAt: new Date(),
              listingId,
            };

            set((state) => ({
              contractAnalyses: [...state.contractAnalyses, newAnalysis],
              analysisInProgress: false,
              analysisError: null,
            }));

            return newAnalysis;
          } else {
            const error = response.message || "Failed to analyze contract";
            set({
              analysisError: error,
              analysisInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage = error.message || "Failed to analyze contract";
          set({
            analysisError: errorMessage,
            analysisInProgress: false,
          });
          throw error;
        }
      },

      deleteContractAnalysis: (analysisId: string) => {
        set((state) => ({
          contractAnalyses: state.contractAnalyses.filter(
            (analysis) => analysis.id !== analysisId
          ),
        }));
      },

      // Market insights actions
      getMarketInsights: async (
        location: string,
        preferences?: UserPreferences
      ) => {
        set({ insightsInProgress: true, insightsError: null });

        try {
          const request: MarketAnalysisRequest = {
            location,
            propertyType: preferences?.propertyTypes?.[0],
            priceRange: preferences
              ? {
                  min: preferences.minPrice,
                  max: preferences.maxPrice,
                }
              : undefined,
            timeframe: "3months",
          };

          const response = await aiService.getMarketAnalysis(request);
          console.log("AIStore - Market insights response:", response);

          if (response.success && response.data) {
            const newInsights: MarketInsights = {
              location,
              analysis: response.data,
              lastUpdated: new Date(),
              userSpecific: !!preferences,
            };

            set((state) => ({
              marketInsights: [
                ...state.marketInsights.filter(
                  (insight) => insight.location !== location
                ),
                newInsights,
              ],
              insightsInProgress: false,
              insightsError: null,
            }));

            return newInsights;
          } else {
            const error = response.message || "Failed to get market insights";
            set({
              insightsError: error,
              insightsInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage = error.message || "Failed to get market insights";
          set({
            insightsError: errorMessage,
            insightsInProgress: false,
          });
          throw error;
        }
      },

      refreshMarketInsights: async (location: string) => {
        const state = get();
        const existingInsights = state.marketInsights.find(
          (insight) => insight.location === location
        );

        if (existingInsights) {
          await get().getMarketInsights(location);
        }
      },

      // Autonomous mode actions
      updateAutonomousSettings: async (
        settings: Partial<AutonomousSettings>
      ) => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          if (!userId) {
            throw new Error("User not authenticated");
          }

          // Map a subset of frontend settings to backend structure (send only what we know)
          const payload: any = {};
          if (typeof settings.enabled === "boolean") {
            payload.enabled = settings.enabled;
          }
          const backendSettings: any = {};
          if (typeof settings.maxApplicationsPerDay === "number") {
            backendSettings.maxApplicationsPerDay =
              settings.maxApplicationsPerDay;
          }
          if (settings.defaultApplicationStyle) {
            const styleMap: Record<string, string> = {
              professional: "professional",
              personal: "casual",
              creative: "expat",
              student: "student",
              expat: "expat",
            };
            const mapped =
              styleMap[settings.defaultApplicationStyle] || "professional";
            backendSettings.applicationTemplate = mapped;
          }
          if (Object.keys(backendSettings).length) {
            payload.settings = backendSettings;
          }
          const criteria: any = {};
          if (typeof settings.autoApplyMaxPrice === "number") {
            criteria.maxPrice = settings.autoApplyMaxPrice;
          }
          if (
            Array.isArray(settings.autoApplyPropertyTypes) &&
            settings.autoApplyPropertyTypes.length
          ) {
            criteria.propertyTypes = settings.autoApplyPropertyTypes;
          }
          if (Object.keys(criteria).length) {
            payload.criteria = criteria;
          }

          // Persist to backend
          await autoApplicationService.updateSettings(userId, payload);

          // Update local state on success
          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              ...settings,
            },
          }));
          return true;
        } catch (error) {
          console.error("Failed to update autonomous settings:", error);
          return false;
        }
      },

      startAutonomousMode: async () => {
        try {
          console.log("🚀 Starting autonomous mode...");
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          console.log("👤 User ID:", userId);
          if (!userId) {
            throw new Error("User not authenticated");
          }

          // Inform backend to enable autonomous mode for this user
          const current = get().autonomousSettings;
          const payload: any = {
            enabled: true,
            settings: {
              maxApplicationsPerDay: Math.max(
                current.maxApplicationsPerDay,
                50
              ), // Ensure at least 50 per day
              applicationTemplate: "professional",
            },
            criteria: {
              maxPrice: current.autoApplyMaxPrice,
              propertyTypes: current.autoApplyPropertyTypes,
            },
          };
          console.log("📤 Sending payload to backend:", payload);
          const response = await autoApplicationService.enableAutoApplication(
            userId,
            payload
          );
          console.log("📥 Backend response:", response);

          // Check if the backend operation was successful
          if (!response.success) {
            throw new Error(
              response.message ||
                response.error ||
                "Failed to enable autonomous mode"
            );
          }

          // Update local state based on backend response
          const backendData = response.data;
          const isBackendEnabled = backendData?.enabled === true;
          const shouldBeActive = isBackendEnabled; // For now, assume enabled = active

          console.log("🔄 Backend data analysis:", {
            backendEnabled: backendData?.enabled,
            backendIsActive: backendData?.status?.isActive,
            shouldBeActive,
            canAutoApply: backendData?.canAutoApply,
            dailyApplicationsRemaining: backendData?.dailyApplicationsRemaining,
            isProfileComplete: backendData?.isProfileComplete,
            documentsComplete: backendData?.documentsComplete,
          });

          // Check why canAutoApply might be false
          if (backendData?.canAutoApply === false) {
            const reasons = [];
            const hasLimitIssue = backendData?.dailyApplicationsRemaining === 0;

            if (hasLimitIssue) {
              reasons.push("Daily application limit reached");
            }
            if (!backendData?.isProfileComplete) {
              reasons.push("User profile incomplete");
            }
            if (!backendData?.documentsComplete) {
              reasons.push("Required documents missing");
            }
            if (reasons.length === 0) {
              reasons.push("Unknown backend restriction");
            }

            console.log("⚠️ Cannot auto-apply because:", reasons.join(", "));

            // If it's just a daily limit issue, try to reset it
            if (hasLimitIssue && reasons.length === 1) {
              try {
                console.log("🔄 Attempting to reset daily limit...");
                const resetResponse =
                  await autoApplicationService.resetDailyLimit(userId);

                if (resetResponse.success) {
                  console.log(
                    "✅ Daily limit reset successfully, retrying enable..."
                  );
                  // Retry the enable operation
                  const retryResponse =
                    await autoApplicationService.enableAutoApplication(
                      userId,
                      payload
                    );

                  if (
                    retryResponse.success &&
                    retryResponse.data?.canAutoApply
                  ) {
                    console.log(
                      "✅ Autonomous mode enabled after daily limit reset!"
                    );
                    shouldBeActive = true; // Allow the mode to proceed
                  } else {
                    console.log("⚠️ Still blocked after daily limit reset");
                  }
                } else {
                  console.log(
                    "❌ Failed to reset daily limit:",
                    resetResponse.message
                  );
                }
              } catch (resetError: any) {
                console.log(
                  "⚠️ Daily limit reset not available:",
                  resetError.message
                );
                // Continue with the original blocking message
              }
            }

            // If still blocked, update the status with a helpful message
            if (!shouldBeActive) {
              let userFriendlyMessage = "";

              if (hasLimitIssue) {
                const todayCount =
                  backendData?.statistics?.dailyApplicationCount || 0;
                const maxPerDay =
                  backendData?.settings?.maxApplicationsPerDay || 20;
                userFriendlyMessage = `Daily limit reached (${todayCount}/${maxPerDay} applications used). Autonomous mode will resume tomorrow.`;
              } else {
                userFriendlyMessage = `Blocked: ${reasons.join(", ")}`;
              }

              set((state) => ({
                autonomousSettings: {
                  ...state.autonomousSettings,
                  enabled: isBackendEnabled,
                },
                autonomousStatus: {
                  ...state.autonomousStatus,
                  isActive: false,
                  currentActivity: undefined,
                  pausedReason: userFriendlyMessage,
                },
              }));

              console.log("📝 User message:", userFriendlyMessage);
              return false; // Don't proceed with runner setup
            }
          }

          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: isBackendEnabled,
            },
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: shouldBeActive,
              currentActivity: shouldBeActive
                ? "Starting autonomous mode..."
                : undefined,
            },
          }));

          // Also try to resume the queue to make sure it's active
          try {
            console.log("▶️ Resuming autonomous queue...");
            const resumeResponse = await autoApplicationService.resumeQueue(
              userId
            );
            console.log("▶️ Queue resume response:", resumeResponse);
          } catch (queueError: any) {
            console.warn(
              "⚠️ Failed to resume queue (this might be normal):",
              queueError.message
            );
            // Don't fail the entire operation if queue resume fails
          }

          // Start autonomous runner with minimal deps
          try {
            const deps = {
              getState: () => ({
                autonomousSettings: get().autonomousSettings,
                autonomousStatus: get().autonomousStatus,
                applications: get().applications,
              }),
              actions: {
                requestPropertyMatching: get().requestPropertyMatching,
                generateApplication: get().generateApplication,
                submitApplication: get().submitApplication,
                addAutonomousActivity: get().addAutonomousActivity,
                enforceApplicationLimits: get().enforceApplicationLimits,
                checkBudgetOverride: get().checkBudgetOverride,
                shouldRequireConfirmation: get().shouldRequireConfirmation,
              },
            } as any;
            const { autonomousRunner } = await import(
              "../services/autonomousRunner"
            );
            autonomousRunner.start(deps);
          } catch (e) {
            console.error("Failed to start autonomous runner:", e);
          }

          get().addAutonomousActivity({
            type: "resumed",
            message: "Autonomous mode started",
            success: true,
          });

          // Clear the "Starting..." message after successful start
          // Wait a moment for the backend to process, then fetch fresh status
          // Use fewer, less aggressive retries to prevent backend spam
          const checkStatusWithRetry = async (attempt = 1, maxAttempts = 2) => {
            try {
              console.log(
                `🔄 Status check attempt ${attempt}/${maxAttempts}...`
              );
              await get().fetchAutonomousStatus(true); // Force fetch to bypass throttling

              const currentStatus = get().autonomousStatus;

              // Only retry once more if status is still inactive
              if (!currentStatus.isActive && attempt < maxAttempts) {
                setTimeout(
                  () => checkStatusWithRetry(attempt + 1, maxAttempts),
                  5000
                ); // Wait 5s between attempts
              } else {
                console.log(
                  `🏁 Status check complete. Final status: isActive=${currentStatus.isActive}`
                );
              }
            } catch (error) {
              console.error(
                `❌ Failed to fetch status on attempt ${attempt}:`,
                error
              );
              // Don't retry on error to prevent spam
            }
          };

          // Start checking after 2 seconds to give backend time
          setTimeout(() => checkStatusWithRetry(), 2000);

          return true;
        } catch (error: any) {
          console.error("❌ Failed to start autonomous mode:", error);
          console.error("❌ Error details:", {
            message: error.message,
            stack: error.stack,
            name: error.name,
            response: error.response?.data,
            status: error.response?.status,
          });

          // Reset the state to show the toggle is off
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: `Error: ${error.message || "Unknown error"}`,
            },
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: false,
            },
          }));

          return false;
        }
      },

      stopAutonomousMode: async () => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          if (!userId) {
            throw new Error("User not authenticated");
          }

          // Inform backend to disable autonomous mode for this user
          await autoApplicationService.disableAutoApplication(userId);

          // Update local state
          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: false,
            },
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: "Manually stopped",
            },
          }));

          try {
            const { autonomousRunner } = await import(
              "../services/autonomousRunner"
            );
            autonomousRunner.stop();
          } catch (e) {
            console.error("Failed to stop autonomous runner:", e);
          }

          get().addAutonomousActivity({
            type: "paused",
            message: "Autonomous mode stopped by user",
            success: true,
          });

          // Fetch fresh data from backend
          await get().fetchAutonomousStatus();
          await get().fetchAutonomousActivities();

          return true;
        } catch (error) {
          console.error("Failed to stop autonomous mode:", error);
          return false;
        }
      },

      pauseAutonomousMode: async (reason: string) => {
        try {
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: reason,
            },
          }));

          // Pause runner without clearing state
          try {
            const { autonomousRunner } = await import(
              "../services/autonomousRunner"
            );
            autonomousRunner.stop();
          } catch (e) {
            console.error("Failed to pause autonomous runner:", e);
          }

          get().addAutonomousActivity({
            type: "paused",
            message: `Autonomous mode paused: ${reason}`,
            success: true,
          });

          // Fetch fresh data from backend
          await get().fetchAutonomousStatus();
          await get().fetchAutonomousActivities();

          return true;
        } catch (error) {
          console.error("Failed to pause autonomous mode:", error);
          return false;
        }
      },

      resumeAutonomousMode: async () => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          if (!userId) {
            throw new Error("User not authenticated");
          }

          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: true,
              currentActivity: "Resuming autonomous mode...",
              pausedReason: undefined,
            },
          }));

          // First, re-enable autonomous mode on the backend
          const currentSettings = get().autonomousSettings;
          console.log("🔄 Re-enabling autonomous mode on backend...");

          const enablePayload = {
            enabled: true,
            settings: {
              maxApplicationsPerDay: Math.max(
                currentSettings.maxApplicationsPerDay || 20,
                50
              ), // Request higher limit
              applicationTemplate: "professional",
              autoSubmit: true,
              requireManualReview: false,
              notificationPreferences: {
                immediate: true,
                daily: true,
                weekly: false,
              },
              language: "english",
            },
            criteria: {
              maxPrice: currentSettings.autoApplyMaxPrice || 2000,
              propertyTypes: currentSettings.autoApplyPropertyTypes || [],
              locations: [],
              excludeKeywords: [],
              includeKeywords: [],
              minRooms: 1,
              maxRooms: 5,
            },
          };

          console.log("📤 Resume payload:", enablePayload);
          const enableResponse =
            await autoApplicationService.enableAutoApplication(
              userId,
              enablePayload
            );
          console.log("📥 Enable response:", enableResponse);

          if (!enableResponse.success) {
            throw new Error(
              `Failed to enable autonomous mode: ${enableResponse.message}`
            );
          }

          // Attempt to resume global processing if needed
          try {
            console.log("▶️ Resuming global processing...");
            const globalResume =
              await autoApplicationService.resumeProcessing();
            console.log("▶️ Global resume response:", globalResume);
          } catch (e) {
            console.warn(
              "Global resume not available or failed:",
              (e as any)?.message
            );
          }

          // Check if we can auto-apply with the response data
          let backendData = enableResponse.data;
          const canAutoApply = backendData?.canAutoApply;
          const dailyRemaining = backendData?.dailyApplicationsRemaining || 0;

          console.log("🔍 Resume analysis:", {
            enabled: backendData?.enabled,
            canAutoApply,
            dailyRemaining,
            maxPerDay: backendData?.settings?.maxApplicationsPerDay,
            todayCount: backendData?.statistics?.dailyApplicationCount,
          });

          // If daily limit is still the issue, try to reset it
          if (!canAutoApply && dailyRemaining === 0) {
            try {
              console.log("🔄 Attempting to reset daily counter...");
              const resetResponse =
                await autoApplicationService.resetDailyLimit(userId);

              if (resetResponse.success) {
                console.log("✅ Daily limit reset succeeded, re-enabling...");
                const retryResponse =
                  await autoApplicationService.enableAutoApplication(
                    userId,
                    enablePayload
                  );

                if (retryResponse.success && retryResponse.data) {
                  backendData = retryResponse.data; // Use the new data
                  console.log("✅ Autonomous mode re-enabled after reset!");
                } else {
                  console.log("⚠️ Re-enable after reset failed");
                }
              } else {
                console.log(
                  "⚠️ Daily limit reset failed:",
                  resetResponse.message
                );
              }
            } catch (resetError: any) {
              console.log(
                "⚠️ Daily limit reset not available:",
                resetError.message
              );
              // Continue without reset
            }
          }

          // Then resume the queue
          console.log("▶️ Resuming queue on backend...");
          const queueResponse = await autoApplicationService.resumeQueue(
            userId
          );
          console.log("▶️ Queue resume response:", queueResponse);

          if (!queueResponse.success) {
            console.warn(
              "Queue resume failed, but continuing...",
              queueResponse.message
            );
          }

          // Update local state based on backend response
          const isEnabled = backendData?.enabled || false;
          const isActive = backendData?.canAutoApply && backendData?.enabled;
          const hasApplicationsRemaining =
            (backendData?.dailyApplicationsRemaining || 0) > 0;

          let statusMessage = undefined;
          let pausedReason = undefined;

          if (!isEnabled) {
            pausedReason = "Autonomous mode disabled on backend";
          } else if (!backendData?.canAutoApply) {
            if ((backendData?.dailyApplicationsRemaining || 0) === 0) {
              const todayCount =
                backendData?.statistics?.dailyApplicationCount || 0;
              const maxPerDay =
                backendData?.settings?.maxApplicationsPerDay || 20;
              pausedReason = `Daily limit reached (${todayCount}/${maxPerDay} applications used). Will reset tomorrow.`;
            } else if (!backendData?.isProfileComplete) {
              pausedReason =
                "Profile incomplete - please complete your profile";
            } else if (!backendData?.documentsComplete) {
              pausedReason = "Missing required documents";
            } else {
              pausedReason = "Autonomous mode temporarily blocked";
            }
          } else {
            statusMessage = "Autonomous mode active";
          }

          console.log("📋 Resume status:", {
            isEnabled,
            isActive,
            hasApplicationsRemaining,
            statusMessage,
            pausedReason,
          });

          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: isEnabled,
            },
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive,
              currentActivity: statusMessage,
              pausedReason,
              applicationsToday:
                backendData?.statistics?.dailyApplicationCount || 0,
              applicationsThisWeek:
                backendData?.statistics?.weeklyApplicationCount || 0,
            },
          }));

          // Resume runner only if mode is active
          if (isActive) {
            try {
              console.log("🚀 Starting autonomous runner...");
              const deps = {
                getState: () => ({
                  autonomousSettings: get().autonomousSettings,
                  autonomousStatus: get().autonomousStatus,
                  applications: get().applications,
                }),
                actions: {
                  requestPropertyMatching: get().requestPropertyMatching,
                  generateApplication: get().generateApplication,
                  submitApplication: get().submitApplication,
                  addAutonomousActivity: get().addAutonomousActivity,
                  enforceApplicationLimits: get().enforceApplicationLimits,
                  checkBudgetOverride: get().checkBudgetOverride,
                  shouldRequireConfirmation: get().shouldRequireConfirmation,
                },
              } as any;
              const { autonomousRunner } = await import(
                "../services/autonomousRunner"
              );
              autonomousRunner.start(deps);
              console.log("✅ Autonomous runner started successfully");
            } catch (e) {
              console.error("Failed to resume autonomous runner:", e);
            }
          }

          get().addAutonomousActivity({
            type: "resumed",
            message: isActive
              ? "Autonomous mode resumed successfully"
              : `Autonomous mode enabled but paused: ${pausedReason}`,
            success: true,
          });

          console.log(
            `🏁 Resume complete. Final status: isEnabled=${isEnabled}, isActive=${isActive}`
          );

          // Only start runner if we're actually active
          if (!isActive) {
            console.log(
              "⚠️ Not starting runner since mode is not active:",
              pausedReason
            );
          }

          return true;
        } catch (error) {
          console.error("Failed to resume autonomous mode:", error);

          // Reset state on failure
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: `Resume failed: ${
                error.message || "Unknown error"
              }`,
            },
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: false,
            },
          }));

          return false;
        }
      },

      getAutonomousStatus: () => {
        return get().autonomousStatus;
      },

      addAutonomousActivity: (
        activity: Omit<AutonomousActivity, "id" | "timestamp">
      ) => {
        const newActivity: AutonomousActivity = {
          ...activity,
          id: `activity_${Date.now()}`,
          timestamp: new Date(),
        };

        set((state) => ({
          autonomousActivities: [
            newActivity,
            ...state.autonomousActivities,
          ].slice(0, 100), // Keep last 100 activities
        }));
      },

      clearAutonomousActivities: () => {
        set({ autonomousActivities: [] });
      },

      fetchAutonomousStatus: async (force: boolean = false) => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          if (!userId) {
            console.warn(
              "User not authenticated, cannot fetch autonomous status"
            );
            return;
          }

          // Throttling: Don't fetch if we fetched recently (unless forced)
          const now = Date.now();
          const lastFetch = get().lastStatusFetch || 0;
          const minInterval = 5000; // 5 seconds minimum between fetches

          if (!force && now - lastFetch < minInterval) {
            console.log("🔄 Status fetch throttled, too recent");
            return;
          }

          // Set fetch timestamp immediately to prevent concurrent calls
          set((state) => ({ ...state, lastStatusFetch: now }));

          // Fetch status from backend
          console.log("📡 Calling backend getStatus()...");
          const statusResponse = await autoApplicationService.getStatus();

          let isEnabled = false;
          let isActive = false;
          let pausedReason: string | undefined = undefined;
          let currentActivity: string | undefined = undefined;
          let applicationsToday = 0;
          let applicationsThisWeek = 0;
          let lastActivity: Date | undefined = undefined;

          if (statusResponse.success && statusResponse.data) {
            const backendStatus = statusResponse.data;
            console.log("📊 Backend status response:", {
              isActive: backendStatus.isActive,
              currentActivity: backendStatus.currentActivity,
              isEnabled: backendStatus.isEnabled,
            });
            isEnabled = !!backendStatus.isEnabled;
            isActive = !!backendStatus.isActive;
            currentActivity = backendStatus.currentActivity || undefined;
            applicationsToday = backendStatus.applicationsToday || 0;
            applicationsThisWeek = backendStatus.applicationsThisWeek || 0;
            lastActivity = backendStatus.lastActivity
              ? new Date(backendStatus.lastActivity)
              : undefined;
          } else {
            console.log("❌ Backend status call failed:", statusResponse);
          }

          // Fetch per-user settings and stats to infer user-specific state
          const [settingsResp, statsResp] = await Promise.allSettled([
            autoApplicationService.getSettings(userId),
            autoApplicationService.getStats(userId),
          ]);

          let maxPerDay: number | undefined = undefined;
          let todayCount: number | undefined = undefined;
          let weeklyCount: number | undefined = undefined;

          if (
            settingsResp.status === "fulfilled" &&
            settingsResp.value.success &&
            settingsResp.value.data
          ) {
            maxPerDay = settingsResp.value.data.settings?.maxApplicationsPerDay;
            // If backend returns enabled in settings, prefer that for per-user toggle
            isEnabled = settingsResp.value.data.enabled ?? isEnabled;
          }
          if (
            statsResp.status === "fulfilled" &&
            statsResp.value.success &&
            statsResp.value.data
          ) {
            const s = statsResp.value.data as any;
            todayCount =
              s.applicationsToday ?? s.statistics?.dailyApplicationCount ?? 0;
            weeklyCount = s.applicationsThisWeek ?? 0;
            applicationsToday = todayCount ?? applicationsToday;
            applicationsThisWeek = weeklyCount ?? applicationsThisWeek;
            lastActivity = s.lastApplicationDate
              ? new Date(s.lastApplicationDate)
              : lastActivity;
          }

          // Derive a better pausedReason than generic "Not active"
          if (!isActive) {
            if (!isEnabled) {
              pausedReason = "Autonomous mode disabled";
            } else if (
              typeof maxPerDay === "number" &&
              typeof applicationsToday === "number" &&
              applicationsToday >= maxPerDay
            ) {
              pausedReason = `Daily limit reached (${applicationsToday}/${maxPerDay} applications used). Will reset tomorrow.`;
            } else {
              // No clear reason; avoid generic scare message
              pausedReason = undefined;
            }
          } else {
            pausedReason = undefined;
          }

          // If enabled and not clearly blocked by daily limit, consider active
          if (!isActive && isEnabled) {
            const blockedByDaily =
              typeof maxPerDay === "number" &&
              typeof applicationsToday === "number" &&
              applicationsToday >= maxPerDay;
            if (!blockedByDaily) {
              isActive = true;
              currentActivity = currentActivity || "Running...";
              pausedReason = undefined;
            }
          }

          // Apply merged status
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive,
              applicationsToday,
              applicationsThisWeek,
              lastActivity,
              currentActivity:
                isActive && !currentActivity ? "Running..." : currentActivity,
              pausedReason,
            },
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: isEnabled,
            },
          }));
          console.log("✅ Status updated (merged per-user + global)", {
            isEnabled,
            isActive,
            pausedReason,
          });

          // Backend status endpoint already checks queue and sets currentActivity
          // No need for separate queue check that might override the backend response
        } catch (error) {
          console.error("Failed to fetch autonomous status:", error);
          // If backend calls fail, clear any stuck currentActivity messages
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              currentActivity: undefined,
            },
          }));
          console.log("🔧 Cleared currentActivity due to backend error");
        }
      },

      fetchAutonomousActivities: async () => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;
          if (!userId) {
            console.warn(
              "User not authenticated, cannot fetch autonomous activities"
            );
            return;
          }

          // Fetch recent application results from backend
          const resultsResponse = await autoApplicationService.getResults(
            userId,
            1,
            50
          );

          if (resultsResponse.success && resultsResponse.data) {
            const results = resultsResponse.data.results || [];

            // Convert backend results to autonomous activities
            const activities: AutonomousActivity[] = results.map(
              (result: any) => {
                const isSuccessful =
                  result.status === "success" ||
                  (result.status === "submitted" &&
                    result.isSuccessful === true);
                return {
                  id: result._id,
                  type: isSuccessful ? "application_submitted" : "error",
                  listingId: result.listingId,
                  propertyTitle: result.listingTitle,
                  message: isSuccessful
                    ? `Application submitted successfully for ${result.listingTitle}`
                    : `Application failed for ${result.listingTitle}: ${
                        result.errorDetails?.message || "Unknown error"
                      }`,
                  timestamp: new Date(result.submittedAt),
                  success: isSuccessful,
                  details: result.errorDetails || undefined,
                };
              }
            );

            // Also fetch queue items for pending/processing activities
            const queueResponse = await autoApplicationService.getQueue(userId);
            if (queueResponse.success && queueResponse.data) {
              const queue = queueResponse.data;

              const queueActivities: AutonomousActivity[] = queue
                .filter(
                  (item: any) =>
                    item.status === "pending" || item.status === "processing"
                )
                .map((item: any) => ({
                  id: `queue_${item._id}`,
                  type: "application_generated" as const,
                  listingId: item.listingId,
                  propertyTitle: item.listingTitle,
                  message:
                    item.status === "processing"
                      ? `Processing application for ${item.listingTitle}`
                      : `Application queued for ${item.listingTitle}`,
                  timestamp: new Date(item.createdAt),
                  success: true,
                }));

              activities.push(...queueActivities);
            }

            // Sort by timestamp (newest first) and update state
            activities.sort(
              (a, b) =>
                new Date(b.timestamp).getTime() -
                new Date(a.timestamp).getTime()
            );

            set({ autonomousActivities: activities.slice(0, 100) }); // Keep last 100 activities
          }
        } catch (error) {
          console.error("Failed to fetch autonomous activities:", error);
          // Don't throw error, just log it so the UI doesn't break
          // The dashboard will show the last known activities or empty state
        }
      },

      // Safety control methods
      checkDailyLimit: () => {
        const state = get();
        return (
          state.autonomousStatus.applicationsToday >=
          state.autonomousSettings.maxApplicationsPerDay
        );
      },

      checkWeeklyLimit: () => {
        const state = get();
        return (
          state.autonomousStatus.applicationsThisWeek >=
          state.autonomousSettings.maxApplicationsPerWeek
        );
      },

      checkBudgetOverride: (propertyPrice: number, userMaxBudget: number) => {
        const state = get();
        const maxAllowedPrice =
          userMaxBudget *
          (1 + state.autonomousSettings.maxBudgetOverride / 100);
        return propertyPrice <= maxAllowedPrice;
      },

      shouldRequireConfirmation: (
        propertyPrice: number,
        userMaxBudget: number
      ) => {
        const state = get();
        if (!state.autonomousSettings.requireConfirmationForExpensive)
          return false;

        const significantlyOverBudget = propertyPrice > userMaxBudget * 1.15; // 15% over budget
        return significantlyOverBudget;
      },

      checkRecentRejections: () => {
        const state = get();
        if (!state.autonomousSettings.pauseOnMultipleRejections) return false;

        // Check for 3 or more rejections in the last 24 hours
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentRejections = state.applications.filter((app) => {
          if (app.response?.status !== "rejected" || !app.response.receivedAt)
            return false;
          const receivedDate =
            typeof app.response.receivedAt === "string"
              ? new Date(app.response.receivedAt)
              : app.response.receivedAt;
          return receivedDate > oneDayAgo;
        });

        return recentRejections.length >= 3;
      },

      enforceApplicationLimits: async () => {
        const state = get();
        const actions = get();

        // Check daily limit
        if (actions.checkDailyLimit()) {
          await actions.pauseAutonomousMode("Daily application limit reached");
          actions.addAutonomousActivity({
            type: "paused",
            message: `Autonomous mode paused: Daily limit of ${state.autonomousSettings.maxApplicationsPerDay} applications reached`,
            success: true,
          });
          return false;
        }

        // Check weekly limit
        if (actions.checkWeeklyLimit()) {
          await actions.pauseAutonomousMode("Weekly application limit reached");
          actions.addAutonomousActivity({
            type: "paused",
            message: `Autonomous mode paused: Weekly limit of ${state.autonomousSettings.maxApplicationsPerWeek} applications reached`,
            success: true,
          });
          return false;
        }

        // Check recent rejections
        if (actions.checkRecentRejections()) {
          await actions.pauseAutonomousMode("Multiple rejections detected");
          actions.addAutonomousActivity({
            type: "paused",
            message:
              "Autonomous mode paused: Multiple rejections detected in the last 24 hours",
            success: true,
          });
          return false;
        }

        return true;
      },

      resetDailyCounters: async () => {
        try {
          const user = useAuthStore.getState().user as any;
          const userId = user?._id || user?.id;

          if (!userId) {
            console.error("User not authenticated for daily counter reset");
            return;
          }

          console.log("🔄 Resetting daily counters via API...");
          const response = await autoApplicationService.resetDailyLimit(userId);

          if (response.success) {
            console.log(
              "✅ Daily limit reset successful, updating local state"
            );
            set((state) => ({
              autonomousStatus: {
                ...state.autonomousStatus,
                applicationsToday: 0,
              },
            }));

            // Refresh autonomous status to get updated data from backend
            get().fetchAutonomousStatus(true); // Force refresh to bypass throttling
          } else {
            console.error("❌ Failed to reset daily limit:", response.message);
            throw new Error(response.message || "Failed to reset daily limit");
          }
        } catch (error: any) {
          console.error("Error resetting daily counters:", error);
          // Still update local state as fallback
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              applicationsToday: 0,
            },
          }));
          throw error;
        }
      },

      resetWeeklyCounters: () => {
        set((state) => ({
          autonomousStatus: {
            ...state.autonomousStatus,
            applicationsThisWeek: 0,
          },
        }));
      },

      // Translation actions
      translateText: async (text: string, targetLanguage: "en" | "nl") => {
        const cacheKey = `${text}_${targetLanguage}`;
        const state = get();

        // Check cache first
        if (state.translationCache.has(cacheKey)) {
          return state.translationCache.get(cacheKey)!;
        }

        set({ translationInProgress: true });

        try {
          const request: TranslationRequest = {
            text,
            targetLanguage,
          };

          const response = await aiService.translateText(request);

          if (response.success && response.data) {
            // Update cache
            const newCache = new Map(state.translationCache);
            newCache.set(cacheKey, response.data);

            set({
              translationCache: newCache,
              translationInProgress: false,
            });

            return response.data;
          } else {
            set({ translationInProgress: false });
            throw new Error(response.message || "Translation failed");
          }
        } catch (error: any) {
          set({ translationInProgress: false });
          throw error;
        }
      },

      clearTranslationCache: () => {
        set({ translationCache: new Map() });
      },

      // Utility actions
      clearError: () => {
        set({
          error: null,
          matchingError: null,
          applicationError: null,
          analysisError: null,
          insightsError: null,
        });
      },

      clearAllData: () => {
        set({
          matches: [],
          applications: [],
          contractAnalyses: [],
          marketInsights: [],
          autonomousActivities: [],
          translationCache: new Map(),
          lastMatchUpdate: null,
          error: null,
          matchingError: null,
          applicationError: null,
          analysisError: null,
          insightsError: null,
        });
      },

      getMatchById: (matchId: string) => {
        return get().matches.find((match) => match.id === matchId);
      },

      getApplicationById: (applicationId: string) => {
        return get().applications.find((app) => app.id === applicationId);
      },

      getAnalysisById: (analysisId: string) => {
        return get().contractAnalyses.find(
          (analysis) => analysis.id === analysisId
        );
      },
    }),
    {
      name: "ai-storage",
      storage: createJSONStorage(() => AsyncStorage),
      // Persist most data but exclude loading states and cache
      partialize: (state) => ({
        matches: state.matches,
        applications: state.applications,
        contractAnalyses: state.contractAnalyses,
        marketInsights: state.marketInsights,
        autonomousSettings: state.autonomousSettings,
        autonomousStatus: state.autonomousStatus,
        autonomousActivities: state.autonomousActivities,
        lastMatchUpdate: state.lastMatchUpdate,
      }),
    }
  )
);

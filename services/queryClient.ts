import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query';
import { apiService } from './api';

// Error handling for queries
const handleQueryError = (error: any) => {
  console.error('Query Error:', error);
  
  // Handle specific error types
  if (error?.status === 401) {
    // Handle unauthorized - could trigger logout
    console.warn('Unauthorized access - user may need to re-authenticate');
  } else if (error?.status === 403) {
    // Handle forbidden
    console.warn('Access forbidden');
  } else if (error?.status >= 500) {
    // Handle server errors
    console.error('Server error occurred');
  } else if (error?.code === 'NETWORK_ERROR') {
    // Handle network errors
    console.warn('Network error - check connection');
  }
};

// Create query client with enhanced configuration
export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: handleQueryError,
  }),
  mutationCache: new MutationCache({
    onError: handleQueryError,
  }),
  defaultOptions: {
    queries: {
      // Stale time - how long data is considered fresh
      staleTime: 5 * 60 * 1000, // 5 minutes
      
      // Cache time - how long data stays in cache after becoming unused
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      
      // Retry configuration
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Refetch on window focus (useful for web)
      refetchOnWindowFocus: false,
      
      // Refetch on reconnect
      refetchOnReconnect: true,
      
      // Background refetch interval
      refetchInterval: false, // Disabled by default, can be enabled per query
      
      // Network mode
      networkMode: 'online',
    },
    mutations: {
      // Retry mutations once
      retry: 1,
      
      // Retry delay for mutations
      retryDelay: 1000,
      
      // Network mode for mutations
      networkMode: 'online',
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth queries
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    preferences: () => [...queryKeys.auth.all, 'preferences'] as const,
  },
  
  // Listings queries
  listings: {
    all: ['listings'] as const,
    lists: () => [...queryKeys.listings.all, 'list'] as const,
    list: (filters: any) => [...queryKeys.listings.lists(), filters] as const,
    details: () => [...queryKeys.listings.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.listings.details(), id] as const,
    saved: () => [...queryKeys.listings.all, 'saved'] as const,
    recent: () => [...queryKeys.listings.all, 'recent'] as const,
    search: (query: string, filters?: any) => [...queryKeys.listings.all, 'search', query, filters] as const,
  },
  
  // AI queries
  ai: {
    all: ['ai'] as const,
    matches: () => [...queryKeys.ai.all, 'matches'] as const,
    match: (preferences: any) => [...queryKeys.ai.matches(), preferences] as const,
    applications: () => [...queryKeys.ai.all, 'applications'] as const,
    application: (id: string) => [...queryKeys.ai.applications(), id] as const,
    contractAnalysis: () => [...queryKeys.ai.all, 'contract-analysis'] as const,
    analysis: (id: string) => [...queryKeys.ai.contractAnalysis(), id] as const,
    marketInsights: () => [...queryKeys.ai.all, 'market-insights'] as const,
    insights: (location: string) => [...queryKeys.ai.marketInsights(), location] as const,
    translation: () => [...queryKeys.ai.all, 'translation'] as const,
    translate: (text: string, targetLang: string) => [...queryKeys.ai.translation(), text, targetLang] as const,
  },
  
  // Notifications queries
  notifications: {
    all: ['notifications'] as const,
    list: () => [...queryKeys.notifications.all, 'list'] as const,
    settings: () => [...queryKeys.notifications.all, 'settings'] as const,
    permissions: () => [...queryKeys.notifications.all, 'permissions'] as const,
  },
};

// Offline support utilities
export const offlineUtils = {
  // Check if device is online
  isOnline: () => {
    if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
      return navigator.onLine;
    }
    return true; // Assume online if can't detect
  },
  
  // Queue mutations for when back online
  queueMutation: (mutationFn: () => Promise<any>, key: string) => {
    // This would typically use a more sophisticated queue
    // For now, we'll use a simple approach with AsyncStorage
    const queuedMutations = JSON.parse(
      localStorage.getItem('queuedMutations') || '[]'
    );
    
    queuedMutations.push({
      key,
      timestamp: Date.now(),
      // Note: We can't serialize functions, so this is a simplified approach
    });
    
    localStorage.setItem('queuedMutations', JSON.stringify(queuedMutations));
  },
  
  // Process queued mutations when back online
  processQueuedMutations: async () => {
    const queuedMutations = JSON.parse(
      localStorage.getItem('queuedMutations') || '[]'
    );
    
    for (const mutation of queuedMutations) {
      try {
        // Process mutation
        console.log('Processing queued mutation:', mutation.key);
        // Implementation would depend on specific mutation type
      } catch (error) {
        console.error('Failed to process queued mutation:', error);
      }
    }
    
    // Clear processed mutations
    localStorage.setItem('queuedMutations', '[]');
  },
};

// Cache invalidation utilities
export const cacheUtils = {
  // Invalidate all auth-related queries
  invalidateAuth: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.auth.all });
  },
  
  // Invalidate all listings queries
  invalidateListings: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.listings.all });
  },
  
  // Invalidate specific listing
  invalidateListing: (id: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.listings.detail(id) });
  },
  
  // Invalidate AI queries
  invalidateAI: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.ai.all });
  },
  
  // Invalidate AI matches
  invalidateMatches: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.ai.matches() });
  },
  
  // Invalidate notifications
  invalidateNotifications: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.notifications.all });
  },
  
  // Clear all cache
  clearAll: () => {
    queryClient.clear();
  },
  
  // Remove specific queries
  removeQueries: (queryKey: any[]) => {
    queryClient.removeQueries({ queryKey });
  },
};

// Prefetch utilities
export const prefetchUtils = {
  // Prefetch user data
  prefetchUser: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.auth.user(),
      queryFn: () => apiService.get('/auth/me'),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  },
  
  // Prefetch recent listings
  prefetchRecentListings: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.listings.recent(),
      queryFn: () => apiService.get('/listings/recent'),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  },
  
  // Prefetch saved listings
  prefetchSavedListings: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.listings.saved(),
      queryFn: () => apiService.get('/listings/saved'),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  },
};

// Background sync utilities
export const backgroundSync = {
  // Setup background refetch for critical data
  setupBackgroundRefetch: () => {
    // Refetch user data every 15 minutes
    queryClient.invalidateQueries({
      queryKey: queryKeys.auth.user(),
      refetchType: 'active',
    });
    
    // Setup interval for background sync
    setInterval(() => {
      if (offlineUtils.isOnline()) {
        // Refetch critical data
        queryClient.invalidateQueries({
          queryKey: queryKeys.auth.user(),
          refetchType: 'active',
        });
        
        queryClient.invalidateQueries({
          queryKey: queryKeys.listings.recent(),
          refetchType: 'active',
        });
      }
    }, 15 * 60 * 1000); // 15 minutes
  },
  
  // Sync when coming back online
  syncOnReconnect: () => {
    // Process any queued mutations
    offlineUtils.processQueuedMutations();
    
    // Refetch stale data
    queryClient.refetchQueries({
      type: 'active',
      stale: true,
    });
  },
};

// Performance monitoring
export const performanceUtils = {
  // Log slow queries
  logSlowQueries: (threshold = 2000) => {
    const originalFetch = queryClient.getQueryCache().build;
    
    queryClient.getQueryCache().build = function(...args) {
      const query = originalFetch.apply(this, args);
      const originalFetch2 = query.fetch;
      
      query.fetch = function(...fetchArgs) {
        const start = Date.now();
        const result = originalFetch2.apply(this, fetchArgs);
        
        if (result instanceof Promise) {
          return result.finally(() => {
            const duration = Date.now() - start;
            if (duration > threshold) {
              console.warn(`Slow query detected: ${JSON.stringify(query.queryKey)} took ${duration}ms`);
            }
          });
        }
        
        return result;
      };
      
      return query;
    };
  },
  
  // Get cache statistics
  getCacheStats: () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
    };
  },
};

// Export configured query client and utilities
export default queryClient;
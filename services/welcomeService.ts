import AsyncStorage from '@react-native-async-storage/async-storage';

const WELCOME_SEEN_KEY = 'welcome_screen_seen';

/**
 * Service to manage welcome screen state
 */
class WelcomeService {
  /**
   * Check if user has seen the welcome screen before
   */
  async hasSeenWelcome(): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(WELCOME_SEEN_KEY);
      return value === 'true';
    } catch (error) {
      console.error('Error checking welcome screen status:', error);
      return false;
    }
  }

  /**
   * Mark welcome screen as seen
   */
  async markWelcomeSeen(): Promise<void> {
    try {
      await AsyncStorage.setItem(WELCOME_SEEN_KEY, 'true');
    } catch (error) {
      console.error('Error marking welcome screen as seen:', error);
    }
  }

  /**
   * Reset welcome screen status (for testing or user preference)
   */
  async resetWelcomeStatus(): Promise<void> {
    try {
      await AsyncStorage.removeItem(WELCOME_SEEN_KEY);
    } catch (error) {
      console.error('Error resetting welcome screen status:', error);
    }
  }

  /**
   * Clear all welcome-related data
   */
  async clearWelcomeData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(WELCOME_SEEN_KEY);
    } catch (error) {
      console.error('Error clearing welcome data:', error);
    }
  }
}

// Export singleton instance
export const welcomeService = new WelcomeService();
export default welcomeService;
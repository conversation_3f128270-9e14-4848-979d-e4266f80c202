import { enhancedAIMatchingService, EnhancedPropertyMatch } from './enhancedAIMatchingService';
import { useAuthStore } from '../store/authStore';
import { useAIStore } from '../store/aiStore';
import { useNotificationStore } from '../store/notificationStore';
import { listingsService } from './listingsService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

export interface RealTimeUpdateConfig {
  enabled: boolean;
  checkInterval: number; // minutes
  notifyOnNewMatches: boolean;
  notifyOnPriceChanges: boolean;
  notifyOnStatusChanges: boolean;
  maxNotificationsPerDay: number;
  quietHours: {
    start: string; // HH:MM
    end: string;   // HH:MM
  };
}

export interface NewMatchNotification {
  type: 'new_match';
  match: EnhancedPropertyMatch;
  timestamp: Date;
}

export interface PriceChangeNotification {
  type: 'price_change';
  listingId: string;
  oldPrice: string;
  newPrice: string;
  change: 'increase' | 'decrease';
  percentage: number;
  timestamp: Date;
}

export interface MatchUpdateNotification {
  type: 'match_update';
  listingId: string;
  updateType: 'available' | 'unavailable' | 'updated';
  message: string;
  timestamp: Date;
}

export type RealTimeNotification = NewMatchNotification | PriceChangeNotification | MatchUpdateNotification;

class RealTimeMatchingService {
  private static instance: RealTimeMatchingService;
  private updateInterval: NodeJS.Timeout | null = null;
  private lastUpdateTimestamp: Date | null = null;
  private notificationsSentToday: number = 0;
  private lastNotificationReset: Date = new Date();
  private isRunning: boolean = false;

  static getInstance(): RealTimeMatchingService {
    if (!RealTimeMatchingService.instance) {
      RealTimeMatchingService.instance = new RealTimeMatchingService();
    }
    return RealTimeMatchingService.instance;
  }

  private constructor() {
    this.loadLastUpdateTimestamp();
    this.resetDailyNotificationCount();
  }

  private async loadLastUpdateTimestamp(): Promise<void> {
    try {
      const timestamp = await AsyncStorage.getItem('last_realtime_update');
      if (timestamp) {
        this.lastUpdateTimestamp = new Date(timestamp);
      }
    } catch (error) {
      console.error('Failed to load last update timestamp:', error);
    }
  }

  private async saveLastUpdateTimestamp(): Promise<void> {
    try {
      await AsyncStorage.setItem('last_realtime_update', new Date().toISOString());
    } catch (error) {
      console.error('Failed to save last update timestamp:', error);
    }
  }

  private resetDailyNotificationCount(): void {
    const now = new Date();
    if (now.getDate() !== this.lastNotificationReset.getDate()) {
      this.notificationsSentToday = 0;
      this.lastNotificationReset = now;
    }
  }

  async startRealTimeUpdates(config: RealTimeUpdateConfig): Promise<void> {
    if (this.isRunning) {
      console.log('Real-time updates already running');
      return;
    }

    console.log('Starting real-time property matching updates');
    this.isRunning = true;

    // Initial check
    await this.performUpdateCheck();

    // Set up periodic checks
    this.updateInterval = setInterval(async () => {
      try {
        await this.performUpdateCheck();
      } catch (error) {
        console.error('Real-time update check failed:', error);
      }
    }, config.checkInterval * 60 * 1000) as any; // Convert minutes to milliseconds
  }

  stopRealTimeUpdates(): void {
    console.log('Stopping real-time property matching updates');
    this.isRunning = false;
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  private async performUpdateCheck(): Promise<void> {
    try {
      const { user } = useAuthStore.getState();
      const { matches: currentMatches } = useAIStore.getState();
      
      if (!user?.preferences) {
        console.log('No user preferences available for real-time updates');
        return;
      }

      console.log('Performing real-time update check...');

      // Fetch latest listings
      const latestListings = await listingsService.getListings({
        limit: 100,
        sortBy: 'dateAdded',
        sortOrder: 'desc'
      });

      // Find new matches since last update
      const newMatches = await enhancedAIMatchingService.findMatches(
        user.preferences,
        user,
        latestListings.data?.listings || []
      );

      // Compare with existing matches to find truly new ones
      const existingMatchIds = new Set(currentMatches.map(m => m.listing._id || m.listing.title));
      const genuinelyNewMatches = newMatches.filter(
        match => !existingMatchIds.has(match.listing._id || match.listing.title)
      );

      if (genuinelyNewMatches.length > 0) {
        console.log(`Found ${genuinelyNewMatches.length} new property matches`);
        
        // Update AI store with new matches
        const { requestPropertyMatching } = useAIStore.getState();
        await requestPropertyMatching(user.preferences, user);

        // Send notifications for new matches
        await this.notifyNewMatches(genuinelyNewMatches);
      }

      // Check for price changes in existing matches
      // Convert PropertyMatch[] to EnhancedPropertyMatch[] format
      const enhancedCurrentMatches: EnhancedPropertyMatch[] = currentMatches.map(match => ({
        ...match,
        matchPercentage: Math.round(match.score * 100),
        keyHighlights: match.reasons || [],
        aiSummary: 'AI analysis available',
        aiInsights: match.pros || [],
        priceAnalysis: {
          marketComparison: 'at' as const,
          valueRating: Math.round(match.score * 100)
        },
        locationScore: 75,
        amenityScore: 75,
        transportScore: 75,
        userInteractions: []
      }));
      
      await this.checkForPriceChanges(enhancedCurrentMatches, latestListings.data?.listings || []);

      // Update timestamp
      this.lastUpdateTimestamp = new Date();
      await this.saveLastUpdateTimestamp();

    } catch (error) {
      console.error('Real-time update check failed:', error);
    }
  }

  private async notifyNewMatches(newMatches: EnhancedPropertyMatch[]): Promise<void> {
    const config = await this.getRealTimeConfig();
    
    if (!config.notifyOnNewMatches || !this.canSendNotification()) {
      return;
    }

    // Sort by match percentage and take top matches
    const topMatches = newMatches
      .sort((a, b) => b.matchPercentage - a.matchPercentage)
      .slice(0, 3); // Limit to top 3 matches

    for (const match of topMatches) {
      if (!this.canSendNotification()) break;

      await this.sendNotification({
        title: `🏠 New ${match.matchPercentage}% Match Found!`,
        body: `${match.listing.title} in ${match.listing.location} - ${match.listing.price}`,
        data: {
          type: 'new_match',
          matchId: match.id,
          listingId: match.listing.id
        }
      });

      this.notificationsSentToday++;
    }

    // Add to notification store
    const { addNotification } = useNotificationStore.getState();
    addNotification({
      title: 'New Property Matches',
      body: `Found ${newMatches.length} new properties matching your preferences`,
      category: 'match',
      priority: 'high',
      data: {
        type: 'new_matches',
        count: newMatches.length,
        action: {
          label: 'View Matches',
          route: '/dashboard'
        }
      }
    });
  }

  private async checkForPriceChanges(
    currentMatches: EnhancedPropertyMatch[],
    latestListings: any[]
  ): Promise<void> {
    const config = await this.getRealTimeConfig();
    
    if (!config.notifyOnPriceChanges) return;

    const priceChanges: PriceChangeNotification[] = [];

    for (const match of currentMatches) {
      const updatedListing = latestListings.find(l => l.id === match.listing.id);
      
      if (updatedListing && updatedListing.price !== match.listing.price) {
        const oldPrice = this.extractNumericPrice(String(match.listing.price));
        const newPrice = this.extractNumericPrice(String(updatedListing.price));
        
        if (oldPrice && newPrice && oldPrice !== newPrice) {
          const percentage = Math.abs((newPrice - oldPrice) / oldPrice * 100);
          
          if (percentage >= 5) { // Only notify for significant changes (5%+)
            priceChanges.push({
              type: 'price_change',
              listingId: match.listing._id || match.listing.title || 'unknown',
              oldPrice: String(match.listing.price),
              newPrice: String(updatedListing.price),
              change: newPrice > oldPrice ? 'increase' : 'decrease',
              percentage: Math.round(percentage),
              timestamp: new Date()
            });
          }
        }
      }
    }

    // Send price change notifications
    for (const change of priceChanges) {
      if (!this.canSendNotification()) break;

      const emoji = change.change === 'decrease' ? '📉' : '📈';
      const action = change.change === 'decrease' ? 'dropped' : 'increased';
      
      await this.sendNotification({
        title: `${emoji} Price ${action.charAt(0).toUpperCase() + action.slice(1)}`,
        body: `Property price ${action} by ${change.percentage}% - now ${change.newPrice}`,
        data: {
          type: 'price_change',
          listingId: change.listingId
        }
      });

      this.notificationsSentToday++;
    }
  }

  private async sendNotification(notification: {
    title: string;
    body: string;
    data?: any;
  }): Promise<void> {
    try {
      if (this.isInQuietHours()) {
        console.log('Skipping notification due to quiet hours');
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data,
          sound: 'default',
        },
        trigger: null, // Send immediately
      });

      console.log('Notification sent:', notification.title);
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  private canSendNotification(): boolean {
    this.resetDailyNotificationCount();
    
    const config = this.getRealTimeConfigSync();
    return this.notificationsSentToday < config.maxNotificationsPerDay;
  }

  private isInQuietHours(): boolean {
    const config = this.getRealTimeConfigSync();
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { start, end } = config.quietHours;
    
    // Handle quiet hours that span midnight
    if (start > end) {
      return currentTime >= start || currentTime <= end;
    } else {
      return currentTime >= start && currentTime <= end;
    }
  }

  private extractNumericPrice(price: string): number | null {
    if (!price) return null;
    const match = price.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : null;
  }

  private async getRealTimeConfig(): Promise<RealTimeUpdateConfig> {
    try {
      const stored = await AsyncStorage.getItem('realtime_config');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load real-time config:', error);
    }

    return this.getDefaultConfig();
  }

  private getRealTimeConfigSync(): RealTimeUpdateConfig {
    return this.getDefaultConfig();
  }

  private getDefaultConfig(): RealTimeUpdateConfig {
    return {
      enabled: true,
      checkInterval: 30, // 30 minutes
      notifyOnNewMatches: true,
      notifyOnPriceChanges: true,
      notifyOnStatusChanges: true,
      maxNotificationsPerDay: 10,
      quietHours: {
        start: '22:00',
        end: '08:00'
      }
    };
  }

  async updateConfig(config: Partial<RealTimeUpdateConfig>): Promise<void> {
    try {
      const currentConfig = await this.getRealTimeConfig();
      const newConfig = { ...currentConfig, ...config };
      
      await AsyncStorage.setItem('realtime_config', JSON.stringify(newConfig));
      
      // Restart updates with new config if running
      if (this.isRunning) {
        this.stopRealTimeUpdates();
        await this.startRealTimeUpdates(newConfig);
      }
    } catch (error) {
      console.error('Failed to update real-time config:', error);
    }
  }

  getStatus(): {
    isRunning: boolean;
    lastUpdate: Date | null;
    notificationsSentToday: number;
    nextUpdate: Date | null;
  } {
    const config = this.getRealTimeConfigSync();
    const nextUpdate = this.lastUpdateTimestamp 
      ? new Date(this.lastUpdateTimestamp.getTime() + config.checkInterval * 60 * 1000)
      : null;

    return {
      isRunning: this.isRunning,
      lastUpdate: this.lastUpdateTimestamp,
      notificationsSentToday: this.notificationsSentToday,
      nextUpdate
    };
  }

  async performManualUpdate(): Promise<{
    newMatches: number;
    priceChanges: number;
    success: boolean;
  }> {
    try {
      const beforeUpdate = useAIStore.getState().matches.length;
      await this.performUpdateCheck();
      const afterUpdate = useAIStore.getState().matches.length;
      
      return {
        newMatches: Math.max(0, afterUpdate - beforeUpdate),
        priceChanges: 0, // Would need to track this separately
        success: true
      };
    } catch (error) {
      console.error('Manual update failed:', error);
      return {
        newMatches: 0,
        priceChanges: 0,
        success: false
      };
    }
  }
}

export const realTimeMatchingService = RealTimeMatchingService.getInstance();

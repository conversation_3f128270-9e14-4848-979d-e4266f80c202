import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface NotificationPreferences {
  immediate: boolean;
  daily: boolean;
  weekly: boolean;
  email: boolean;
  sms: boolean;
  push: boolean;
}

export interface NotificationHistoryItem {
  id: string;
  type: 'application_status_update' | 'daily_summary' | 'weekly_summary' | 'urgent_alert' | 'system_maintenance';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  channels: string[];
  data?: any;
}

export interface NotificationStats {
  totalSent: number;
  delivered: number;
  failed: number;
  deliveryRate: number;
  channelStats: {
    email: { sent: number; delivered: number; rate: number };
    sms: { sent: number; delivered: number; rate: number };
    push: { sent: number; delivered: number; rate: number };
  };
  typeStats: Record<string, number>;
  last30Days: {
    dates: string[];
    counts: number[];
  };
}

export interface SchedulerStatus {
  initialized: boolean;
  activeJobs: string[];
  runningJobs: Array<{
    name: string;
    running: boolean;
  }>;
}

/**
 * NotificationService - Frontend service for managing notifications
 * 
 * This service provides:
 * - Notification preferences management
 * - Notification history retrieval
 * - Test notification sending
 * - Push notification registration
 * - Real-time notification handling
 */
class NotificationService {
  private baseUrl: string;
  private authToken: string | null = null;
  private pushToken: string | null = null;
  private notificationListeners: Array<(notification: NotificationHistoryItem) => void> = [];

  constructor() {
    this.baseUrl = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3001';
    this.loadAuthToken();
  }

  /**
   * Load authentication token from storage
   */
  private async loadAuthToken(): Promise<void> {
    try {
      this.authToken = await AsyncStorage.getItem('authToken');
    } catch (error) {
      console.error('Error loading auth token:', error);
    }
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * Get request headers with authentication
   */
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  /**
   * Get user notification preferences
   */
  async getNotificationPreferences(): Promise<NotificationPreferences> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/preferences`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get notification preferences');
      }

      return data.data;
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      throw error;
    }
  }

  /**
   * Update user notification preferences
   */
  async updateNotificationPreferences(preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/preferences`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(preferences),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to update notification preferences');
      }

      return data.data;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get notification history
   */
  async getNotificationHistory(options: {
    limit?: number;
    offset?: number;
    type?: string;
  } = {}): Promise<{
    notifications: NotificationHistoryItem[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      const params = new URLSearchParams();
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.type) params.append('type', options.type);

      const response = await fetch(`${this.baseUrl}/api/notifications/history?${params}`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get notification history');
      }

      // Convert timestamp strings to Date objects
      const notifications = data.data.notifications.map((notification: any) => ({
        ...notification,
        timestamp: new Date(notification.timestamp),
      }));

      return {
        ...data.data,
        notifications,
      };
    } catch (error) {
      console.error('Error getting notification history:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to delete notification');
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(): Promise<NotificationStats> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/stats`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get notification statistics');
      }

      return data.data;
    } catch (error) {
      console.error('Error getting notification statistics:', error);
      throw error;
    }
  }

  /**
   * Send test daily summary notification
   */
  async sendTestDailySummary(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/test-daily-summary`, {
        method: 'POST',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to send test daily summary');
      }
    } catch (error) {
      console.error('Error sending test daily summary:', error);
      throw error;
    }
  }

  /**
   * Send test weekly summary notification
   */
  async sendTestWeeklySummary(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/test-weekly-summary`, {
        method: 'POST',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to send test weekly summary');
      }
    } catch (error) {
      console.error('Error sending test weekly summary:', error);
      throw error;
    }
  }

  /**
   * Send test urgent alert notification
   */
  async sendTestUrgentAlert(alertType: string = 'system_error', message: string = 'This is a test alert'): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/test-urgent-alert`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ alertType, message }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to send test urgent alert');
      }
    } catch (error) {
      console.error('Error sending test urgent alert:', error);
      throw error;
    }
  }

  /**
   * Get scheduler status (admin only)
   */
  async getSchedulerStatus(): Promise<SchedulerStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/scheduler/status`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get scheduler status');
      }

      return data.data;
    } catch (error) {
      console.error('Error getting scheduler status:', error);
      throw error;
    }
  }

  /**
   * Send maintenance notification (admin only)
   */
  async sendMaintenanceNotification(maintenanceData: {
    scheduledStart: Date;
    estimatedDuration: string;
    affectedServices?: string[];
    impact?: string;
    alternativeActions?: string[];
    contactInfo?: string;
  }): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/maintenance`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          ...maintenanceData,
          scheduledStart: maintenanceData.scheduledStart.toISOString(),
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to send maintenance notification');
      }
    } catch (error) {
      console.error('Error sending maintenance notification:', error);
      throw error;
    }
  }

  /**
   * Register for push notifications
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Web push notifications would be handled differently
        console.log('Web push notifications not implemented yet');
        return null;
      }

      // Check if running in Expo Go
      const isExpoGo = __DEV__ && !process.env.EXPO_PUBLIC_USE_DEV_BUILD;

      if (isExpoGo) {
        console.warn('Push notifications are not supported in Expo Go with SDK 53+. Use a development build instead.');
        return null;
      }

      // Only attempt to register if not in Expo Go
      try {
        const ExpoNotifications = await import('expo-notifications');
        const Notifications = ExpoNotifications.default;

        // Request permissions
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          console.warn('Push notification permissions not granted');
          return null;
        }

        // Get push token
        const token = await Notifications.getExpoPushTokenAsync({
          projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
        });

        this.pushToken = token.data;

        // Register token with backend
        await this.registerPushTokenWithBackend(token.data);

        return token.data;
      } catch (notificationError) {
        console.warn('Push notifications not available:', notificationError);
        return null;
      }

    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  }

  /**
   * Handle incoming push notification
   */
  handlePushNotification(notification: any): void {
    try {
      const notificationItem: NotificationHistoryItem = {
        id: notification.id || Date.now().toString(),
        type: notification.data?.type || 'application_status_update',
        title: notification.title || 'ZakMakelaar Notification',
        message: notification.body || notification.message || '',
        timestamp: new Date(),
        read: false,
        channels: ['push'],
        data: notification.data,
      };

      // Notify all listeners
      this.notificationListeners.forEach(listener => {
        try {
          listener(notificationItem);
        } catch (error) {
          console.error('Error in notification listener:', error);
        }
      });

      // Store notification locally for history
      this.storeNotificationLocally(notificationItem);

    } catch (error) {
      console.error('Error handling push notification:', error);
    }
  }

  /**
   * Add notification listener
   */
  addNotificationListener(listener: (notification: NotificationHistoryItem) => void): () => void {
    this.notificationListeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.notificationListeners.indexOf(listener);
      if (index > -1) {
        this.notificationListeners.splice(index, 1);
      }
    };
  }

  /**
   * Store notification locally for offline access
   */
  private async storeNotificationLocally(notification: NotificationHistoryItem): Promise<void> {
    try {
      const key = 'local_notifications';
      const existingNotifications = await AsyncStorage.getItem(key);
      const notifications = existingNotifications ? JSON.parse(existingNotifications) : [];

      // Add new notification to the beginning
      notifications.unshift(notification);

      // Keep only the last 100 notifications
      const trimmedNotifications = notifications.slice(0, 100);

      await AsyncStorage.setItem(key, JSON.stringify(trimmedNotifications));
    } catch (error) {
      console.error('Error storing notification locally:', error);
    }
  }

  /**
   * Get locally stored notifications
   */
  async getLocalNotifications(): Promise<NotificationHistoryItem[]> {
    try {
      const key = 'local_notifications';
      const storedNotifications = await AsyncStorage.getItem(key);

      if (!storedNotifications) {
        return [];
      }

      const notifications = JSON.parse(storedNotifications);

      // Convert timestamp strings back to Date objects
      return notifications.map((notification: any) => ({
        ...notification,
        timestamp: new Date(notification.timestamp),
      }));
    } catch (error) {
      console.error('Error getting local notifications:', error);
      return [];
    }
  }

  /**
   * Clear local notification storage
   */
  async clearLocalNotifications(): Promise<void> {
    try {
      await AsyncStorage.removeItem('local_notifications');
    } catch (error) {
      console.error('Error clearing local notifications:', error);
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(): Promise<number> {
    try {
      const localNotifications = await this.getLocalNotifications();
      return localNotifications.filter(n => !n.read).length;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Mark all notifications as read locally
   */
  async markAllAsReadLocally(): Promise<void> {
    try {
      const key = 'local_notifications';
      const storedNotifications = await AsyncStorage.getItem(key);

      if (!storedNotifications) {
        return;
      }

      const notifications = JSON.parse(storedNotifications);
      const updatedNotifications = notifications.map((notification: any) => ({
        ...notification,
        read: true,
      }));

      await AsyncStorage.setItem(key, JSON.stringify(updatedNotifications));
    } catch (error) {
      console.error('Error marking all notifications as read locally:', error);
    }
  }

  /**
   * Format notification for display
   */
  formatNotificationForDisplay(notification: NotificationHistoryItem): {
    title: string;
    subtitle: string;
    icon: string;
    color: string;
    timeAgo: string;
  } {
    const now = new Date();
    const diffMs = now.getTime() - notification.timestamp.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    let timeAgo: string;
    if (diffMinutes < 1) {
      timeAgo = 'Just now';
    } else if (diffMinutes < 60) {
      timeAgo = `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      timeAgo = `${diffHours}h ago`;
    } else {
      timeAgo = `${diffDays}d ago`;
    }

    const typeConfig = {
      application_status_update: {
        icon: '📋',
        color: '#007bff',
      },
      daily_summary: {
        icon: '📊',
        color: '#28a745',
      },
      weekly_summary: {
        icon: '📈',
        color: '#17a2b8',
      },
      urgent_alert: {
        icon: '🚨',
        color: '#dc3545',
      },
      system_maintenance: {
        icon: '🔧',
        color: '#ffc107',
      },
    };

    const config = typeConfig[notification.type] || {
      icon: '📱',
      color: '#6c757d',
    };

    return {
      title: notification.title,
      subtitle: notification.message,
      icon: config.icon,
      color: config.color,
      timeAgo,
    };
  }

  /**
   * Check if notifications are enabled for the current platform
   */
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // Check web notification permission
        if ('Notification' in window) {
          return Notification.permission === 'granted';
        }
        return false;
      }

      // Check if running in Expo Go
      const isExpoGo = __DEV__ && !process.env.EXPO_PUBLIC_USE_DEV_BUILD;

      if (isExpoGo) {
        console.warn('Push notifications are not supported in Expo Go with SDK 53+');
        return false;
      }

      try {
        const ExpoNotifications = await import('expo-notifications');
        const Notifications = ExpoNotifications.default;
        const { status } = await Notifications.getPermissionsAsync();
        return status === 'granted';
      } catch (notificationError) {
        console.warn('Push notifications not available:', notificationError);
        return false;
      }
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }

  /**
   * Request notification permissions
   */
  async requestNotificationPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // Request web notification permission
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          return permission === 'granted';
        }
        return false;
      }

      // Check if running in Expo Go
      const isExpoGo = __DEV__ && !process.env.EXPO_PUBLIC_USE_DEV_BUILD;

      if (isExpoGo) {
        console.warn('Push notifications are not supported in Expo Go with SDK 53+');
        return false;
      }

      try {
        const ExpoNotifications = await import('expo-notifications');
        const Notifications = ExpoNotifications.default;
        const { status } = await Notifications.requestPermissionsAsync();
        return status === 'granted';
      } catch (notificationError) {
        console.warn('Push notifications not available:', notificationError);
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * Register push token with backend
   */
  private async registerPushTokenWithBackend(token: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/notifications/register-push-token`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ pushToken: token }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to register push token');
      }
    } catch (error) {
      console.error('Error registering push token with backend:', error);
      throw error;
    }
  }

  /**
   * Check if push notifications are supported in current environment
   */
  isPushNotificationSupported(): boolean {
    if (Platform.OS === 'web') {
      return 'Notification' in window;
    }

    // Check if running in Expo Go
    const isExpoGo = __DEV__ && !process.env.EXPO_PUBLIC_USE_DEV_BUILD;

    if (isExpoGo) {
      return false;
    }

    return true;
  }
}

export default new NotificationService();
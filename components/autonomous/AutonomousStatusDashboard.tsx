import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  FadeIn,
  FadeInUp,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { useAIStore, AutonomousActivity } from '../../store/aiStore';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Real-time Status Card Component
const RealTimeStatusCard = ({
  isActive,
  currentActivity,
  applicationsToday,
  applicationsThisWeek,
  onToggle,
  onPause,
  onResume,
  pausedReason,
}: {
  isActive: boolean;
  currentActivity?: string;
  applicationsToday: number;
  applicationsThisWeek: number;
  onToggle: () => void;
  onPause: () => void;
  onResume: () => void;
  pausedReason?: string;
}) => {
  const pulseScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0.5);

  useEffect(() => {
    if (isActive) {
      // Pulse animation for active status
      pulseScale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
          withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
        ),
        -1,
        true
      );

      // Glow animation
      glowOpacity.value = withRepeat(
        withSequence(
          withTiming(0.8, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
          withTiming(0.5, { duration: 2000, easing: Easing.inOut(Easing.ease) })
        ),
        -1,
        true
      );
    } else {
      pulseScale.value = withTiming(1, { duration: 300 });
      glowOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isActive, pulseScale, glowOpacity]);

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const handleToggle = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onToggle();
  };

  const handlePause = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Alert.alert(
      'Pause Autonomous Mode',
      'Are you sure you want to pause autonomous mode?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Pause', style: 'destructive', onPress: onPause },
      ]
    );
  };

  const handleResume = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onResume();
  };

  return (
    <Animated.View
      style={styles.statusCard}
      entering={FadeIn.duration(600)}
    >
      {/* Glow effect for active status */}
      {isActive && (
        <Animated.View style={[styles.statusGlow, glowStyle]}>
          <LinearGradient
            colors={[THEME.success, THEME.primary]}
            style={styles.statusGlowGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>
      )}

      <LinearGradient
        colors={isActive ? [THEME.success, THEME.primary] : [THEME.lightGray, THEME.gray]}
        style={styles.statusCardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.statusHeader}>
          <Animated.View style={[styles.statusIndicatorContainer, pulseStyle]}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: isActive ? THEME.light : THEME.dark }
            ]}>
              <Ionicons
                name={isActive ? "flash" : "flash-off"}
                size={24}
                color={isActive ? THEME.success : THEME.light}
              />
            </View>
          </Animated.View>

          <View style={styles.statusInfo}>
            <Text style={[
              styles.statusTitle,
              { color: isActive ? THEME.light : THEME.dark }
            ]}>
              Autonomous Mode
            </Text>
            <Text style={[
              styles.statusSubtitle,
              { color: isActive ? 'rgba(255,255,255,0.9)' : THEME.gray }
            ]}>
              {isActive ? 'Active' : pausedReason ? `Paused: ${pausedReason}` : 'Inactive'}
            </Text>
            {currentActivity && (
              <Text style={[
                styles.statusActivity,
                { color: isActive ? 'rgba(255,255,255,0.8)' : THEME.gray }
              ]}>
                {currentActivity}
              </Text>
            )}
          </View>

          <View style={styles.statusControls}>
            {isActive ? (
              <TouchableOpacity
                style={[styles.controlButton, styles.pauseButton]}
                onPress={handlePause}
              >
                <Ionicons name="pause" size={20} color={THEME.light} />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.controlButton, styles.resumeButton]}
                onPress={pausedReason ? handleResume : handleToggle}
              >
                <Ionicons name="play" size={20} color={THEME.light} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.statusMetrics}>
          <View style={styles.statusMetric}>
            <Text style={[
              styles.statusMetricValue,
              { color: isActive ? THEME.light : THEME.dark }
            ]}>
              {applicationsToday}
            </Text>
            <Text style={[
              styles.statusMetricLabel,
              { color: isActive ? 'rgba(255,255,255,0.8)' : THEME.gray }
            ]}>
              Today
            </Text>
          </View>

          <View style={styles.statusDivider} />

          <View style={styles.statusMetric}>
            <Text style={[
              styles.statusMetricValue,
              { color: isActive ? THEME.light : THEME.dark }
            ]}>
              {applicationsThisWeek}
            </Text>
            <Text style={[
              styles.statusMetricLabel,
              { color: isActive ? 'rgba(255,255,255,0.8)' : THEME.gray }
            ]}>
              This Week
            </Text>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

// Enhanced Activity Log Item Component
const ActivityLogItem = ({
  activity,
  index,
  totalActivities
}: {
  activity: AutonomousActivity;
  index: number;
  totalActivities: number;
}) => {
  const getActivityIcon = (type: AutonomousActivity['type']) => {
    switch (type) {
      case 'application_generated':
        return 'document-text';
      case 'application_submitted':
        return 'paper-plane';
      case 'match_found':
        return 'search';
      case 'error':
        return 'warning';
      case 'paused':
        return 'pause';
      case 'resumed':
        return 'play';
      default:
        return 'information-circle';
    }
  };

  const getActivityGradient = (type: AutonomousActivity['type'], success: boolean) => {
    if (!success) return [THEME.danger, '#dc2626'];

    switch (type) {
      case 'application_generated':
        return [THEME.primary, THEME.secondary];
      case 'application_submitted':
        return [THEME.success, '#059669'];
      case 'match_found':
        return [THEME.accent, THEME.secondary];
      case 'error':
        return [THEME.danger, '#dc2626'];
      case 'paused':
        return [THEME.warning, '#d97706'];
      case 'resumed':
        return [THEME.success, '#059669'];
      default:
        return [THEME.gray, '#4b5563'];
    }
  };

  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    }).format(dateObj);
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (dateObj.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (dateObj.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(dateObj);
    }
  };

  const getStatusBadge = (type: AutonomousActivity['type'], success: boolean) => {
    if (!success) {
      return { text: 'Failed', color: THEME.danger };
    }

    switch (type) {
      case 'application_generated':
        return { text: 'Generated', color: THEME.primary };
      case 'application_submitted':
        return { text: 'Submitted', color: THEME.success };
      case 'match_found':
        return { text: 'Match Found', color: THEME.accent };
      case 'error':
        return { text: 'Error', color: THEME.danger };
      case 'paused':
        return { text: 'Paused', color: THEME.warning };
      case 'resumed':
        return { text: 'Resumed', color: THEME.success };
      default:
        return { text: 'Info', color: THEME.gray };
    }
  };

  const statusBadge = getStatusBadge(activity.type, activity.success);

  return (
    <Animated.View
      style={styles.activityItem}
      entering={FadeInUp.duration(400).delay(index * 50)}
    >
      <View style={styles.activityLeftSection}>
        <LinearGradient
          colors={getActivityGradient(activity.type, activity.success) as any}
          style={styles.activityIcon}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons
            name={getActivityIcon(activity.type) as any}
            size={18}
            color={THEME.light}
          />
        </LinearGradient>

        {index < totalActivities - 1 && (
          <View style={styles.activityConnector} />
        )}
      </View>

      <View style={styles.activityContent}>
        <View style={styles.activityContentHeader}>
          <View style={styles.activityTitleRow}>
            <Text style={styles.activityMessage}>{activity.message}</Text>
            <View style={[styles.statusBadge, { backgroundColor: `${statusBadge.color}15` }]}>
              <Text style={[styles.statusBadgeText, { color: statusBadge.color }]}>
                {statusBadge.text}
              </Text>
            </View>
          </View>

          <View style={styles.activityTimeContainer}>
            <Ionicons name="time-outline" size={12} color={THEME.gray} />
            <Text style={styles.activityTime}>
              {formatDate(activity.timestamp)} at {formatTime(activity.timestamp)}
            </Text>
          </View>
        </View>

        {activity.propertyTitle && (
          <View style={styles.propertyContainer}>
            <Ionicons name="home-outline" size={14} color={THEME.primary} />
            <Text style={styles.activityProperty}>
              {activity.propertyTitle}
            </Text>
          </View>
        )}

        {activity.details && (
          <View style={styles.detailsContainer}>
            <Text style={styles.activityDetails}>
              {typeof activity.details === 'string'
                ? activity.details
                : JSON.stringify(activity.details, null, 2)
              }
            </Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

// Main Component
export const AutonomousStatusDashboard = () => {
  const router = useRouter();
  const {
    autonomousStatus,
    autonomousActivities = [], // Add default value as safety check
    startAutonomousMode,
    stopAutonomousMode,
    pauseAutonomousMode,
    resumeAutonomousMode,
    clearAutonomousActivities,
    fetchAutonomousStatus,
    fetchAutonomousActivities,
  } = useAIStore();

  const [refreshing, setRefreshing] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Fetch real data when component mounts (no periodic refresh to prevent spam)
  useEffect(() => {
    const fetchData = async () => {
      try {
        await Promise.all([
          fetchAutonomousStatus(true), // Force initial fetch
          fetchAutonomousActivities(),
        ]);
      } catch (error) {
        console.error('Failed to fetch initial autonomous data:', error);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchData();
    
    // No periodic refresh - rely on user interactions and manual refresh
  }, [fetchAutonomousStatus, fetchAutonomousActivities]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchAutonomousStatus(true), // Force refresh
        fetchAutonomousActivities(),
      ]);
    } catch (error) {
      console.error('Failed to refresh autonomous data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleToggleAutonomous = async () => {
    try {
      if (autonomousStatus.isActive) {
        await stopAutonomousMode();
      } else {
        await startAutonomousMode();
      }

      // State change will be handled by the store's internal logic
      // No need to fetch here as it creates spam

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to toggle autonomous mode');
    }
  };

  const handlePause = async () => {
    try {
      await pauseAutonomousMode('Manually paused by user');

      // State changes handled by store logic

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to pause autonomous mode');
    }
  };

  const handleResume = async () => {
    try {
      await resumeAutonomousMode();

      // State changes handled by store logic

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to resume autonomous mode');
    }
  };

  const handleClearActivities = () => {
    Alert.alert(
      'Clear Activity Log',
      'Are you sure you want to clear all activity logs?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            clearAutonomousActivities();
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        },
      ]
    );
  };

  const handleOpenSettings = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push('/autonomous-settings');
  };

  if (initialLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <Animated.View
          style={styles.loadingContent}
          entering={FadeIn.duration(400)}
        >
          <Ionicons name="flash" size={48} color={THEME.primary} />
          <Text style={styles.loadingText}>Loading Autonomous Status...</Text>
        </Animated.View>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={THEME.primary}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Real-time Status Card */}
      <RealTimeStatusCard
        isActive={autonomousStatus.isActive}
        currentActivity={autonomousStatus.currentActivity}
        applicationsToday={autonomousStatus.applicationsToday}
        applicationsThisWeek={autonomousStatus.applicationsThisWeek}
        onToggle={handleToggleAutonomous}
        onPause={handlePause}
        onResume={handleResume}
        pausedReason={autonomousStatus.pausedReason}
      />

      {/* Activity Log Section */}
      <Animated.View
        style={styles.activitySection}
        entering={FadeInUp.duration(600).delay(300)}
      >
        <View style={styles.activityHeader}>
          <Text style={styles.activityTitle}>Activity Log</Text>
          <View style={styles.activityHeaderActions}>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={handleOpenSettings}
            >
              <Text style={styles.settingsButtonText}>Settings</Text>
              <Ionicons name="settings-outline" size={16} color={THEME.primary} />
            </TouchableOpacity>
            {autonomousActivities.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClearActivities}
              >
                <Text style={styles.clearButtonText}>Clear</Text>
                <Ionicons name="trash-outline" size={16} color={THEME.danger} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {autonomousActivities.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={48} color={THEME.gray} />
            <Text style={styles.emptyStateTitle}>No Activity Yet</Text>
            <Text style={styles.emptyStateDescription}>
              Autonomous activities will appear here when they occur
            </Text>
          </View>
        ) : (
          <View style={styles.activityList}>
            {autonomousActivities.map((activity, index) => (
              <ActivityLogItem
                key={activity.id}
                activity={activity}
                index={index}
                totalActivities={autonomousActivities.length}
              />
            ))}
          </View>
        )}
      </Animated.View>

      <View style={styles.bottomSpacer} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  statusCard: {
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 10,
  },
  statusGlow: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 20,
    overflow: 'hidden',
  },
  statusGlowGradient: {
    width: '100%',
    height: '100%',
  },
  statusCardGradient: {
    padding: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusIndicatorContainer: {
    marginRight: 16,
  },
  statusIndicator: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  statusActivity: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  statusControls: {
    marginLeft: 16,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  pauseButton: {
    backgroundColor: THEME.warning,
  },
  resumeButton: {
    backgroundColor: THEME.success,
  },
  statusMetrics: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusMetric: {
    alignItems: 'center',
    flex: 1,
  },
  statusDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 20,
  },
  statusMetricValue: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 4,
  },
  statusMetricLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  activitySection: {
    backgroundColor: THEME.light,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  activityHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  activityTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.dark,
  },
  settingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(67, 97, 238, 0.2)',
  },
  settingsButtonText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '600',
    marginRight: 6,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.2)',
  },
  clearButtonText: {
    fontSize: 14,
    color: THEME.danger,
    fontWeight: '600',
    marginRight: 6,
  },
  activityList: {
    padding: 24,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  activityLeftSection: {
    alignItems: 'center',
    marginRight: 16,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  activityConnector: {
    width: 2,
    flex: 1,
    backgroundColor: '#e5e7eb',
    marginTop: 8,
    minHeight: 20,
  },
  activityContent: {
    flex: 1,
    backgroundColor: THEME.lightGray,
    borderRadius: 16,
    padding: 16,
  },
  activityContentHeader: {
    marginBottom: 12,
  },
  activityTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  activityMessage: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    flex: 1,
    marginRight: 12,
    lineHeight: 22,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  activityTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityTime: {
    fontSize: 13,
    color: THEME.gray,
    fontWeight: '500',
    marginLeft: 4,
  },
  propertyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 8,
  },
  activityProperty: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '600',
    marginLeft: 6,
  },
  detailsContainer: {
    backgroundColor: 'rgba(107, 114, 128, 0.1)',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: THEME.gray,
  },
  activityDetails: {
    fontSize: 13,
    color: THEME.gray,
    fontFamily: 'monospace',
    lineHeight: 18,
  },
  emptyState: {
    alignItems: 'center',
    padding: 48,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.dark,
    marginTop: 20,
    marginBottom: 12,
  },
  emptyStateDescription: {
    fontSize: 15,
    color: THEME.gray,
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
  bottomSpacer: {
    height: 32,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: THEME.gray,
    marginTop: 16,
    fontWeight: '500',
  },
});
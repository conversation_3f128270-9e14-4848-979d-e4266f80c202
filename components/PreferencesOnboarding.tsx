import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, KeyboardAvoidingView, Platform, Alert, BackHandler } from 'react-native';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { navigationService } from '../services/navigationService';
import { OnboardingNavigator } from './OnboardingNavigator';
import { OnboardingContextHelp } from './OnboardingContextHelp';
import { useAuthStore } from '../store/authStore';
import { PreferencesValidationService } from '../services/preferencesValidationService';
import { ValidationFeedback } from './preferences/ValidationFeedback';
import { LogService } from '../services/logService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

// Define onboarding steps for the preferences screen
const ONBOARDING_STEPS = [
  {
    id: 'welcome',
    title: 'Welcome',
    route: '/',
    helpItems: [
      {
        title: 'AI-Powered Experience',
        content: 'ZakMakelaar uses advanced AI to find your perfect rental property and handle applications automatically.',
        icon: 'bulb-outline'
      },
      {
        title: 'Onboarding Process',
        content: 'The onboarding consists of 3 simple steps: welcome, authentication, and preferences setup.',
        icon: 'list-outline'
      },
      {
        title: 'Autonomous Features',
        content: 'After setup, our AI can automatically find and apply to properties that match your criteria.',
        icon: 'flash-outline'
      }
    ]
  },
  {
    id: 'auth',
    title: 'Authentication',
    route: '/login',
    helpItems: [
      {
        title: 'Secure Login',
        content: 'Your credentials are securely encrypted and never stored in plain text.',
        icon: 'shield-checkmark-outline'
      },
      {
        title: 'Account Benefits',
        content: 'Creating an account enables personalized AI matching and autonomous application features.',
        icon: 'person-add-outline'
      },
      {
        title: 'Data Privacy',
        content: 'We only collect information necessary for finding your ideal rental property.',
        icon: 'lock-closed-outline'
      },
      {
        title: 'Social Login',
        content: 'Coming soon: Quick login with Google and Apple accounts for faster access.',
        icon: 'logo-google'
      }
    ]
  },
  {
    id: 'preferences',
    title: 'Preferences',
    route: '/preferences',
    helpItems: [
      {
        title: 'AI Matching',
        content: 'The preferences you set will be used by our AI to find properties that perfectly match your needs.',
        icon: 'options-outline'
      },
      {
        title: 'Smart Defaults',
        content: 'We provide intelligent default values based on market data and popular choices.',
        icon: 'analytics-outline'
      },
      {
        title: 'Autonomous Mode',
        content: 'Your preferences determine which properties the AI will automatically apply for when enabled.',
        icon: 'rocket-outline'
      },
      {
        title: 'Updating Later',
        content: 'You can always update your preferences later from your profile settings.',
        icon: 'refresh-outline'
      }
    ]
  }
];

interface PreferencesOnboardingProps {
  children: React.ReactElement<{
    onValidationChange?: (isValid: boolean) => void;
    [key: string]: any;  // Allow other props that might be passed to the child
  }>;
  isOnboarding?: boolean;
}

export const PreferencesOnboarding: React.FC<PreferencesOnboardingProps> = ({
  children,
  isOnboarding = true
}) => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [isWizardComplete, setIsWizardComplete] = useState(false);
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const [arePreferencesValid, setArePreferencesValid] = useState(false);

  // Handle back button press and navigation attempts
  useEffect(() => {
    // Handle hardware back button on Android
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // If wizard is not complete, show confirmation dialog
      if (isOnboarding && !isWizardComplete) {
        setShowExitConfirmation(true);
        return true; // Prevent default back behavior
      }
      return false; // Allow default back behavior
    });

    return () => backHandler.remove();
  }, [isOnboarding, isWizardComplete]);

  // Handle exit confirmation
  const handleExitConfirm = () => {
    console.log('handleExitConfirm called');
    LogService.info('PreferencesOnboarding', 'User confirmed exit from preferences wizard');
    setShowExitConfirmation(false);
    // Navigate back to previous screen
    router.back();
  };

  const handleExitCancel = () => {
    console.log('handleExitCancel called');
    LogService.info('PreferencesOnboarding', 'User canceled exit from preferences wizard');
    setShowExitConfirmation(false);
    // Stay on current screen
  };

  // This function is no longer used - navigation is handled by the parent component
  // We're keeping it as a stub to avoid breaking any references
  const handleComplete = async () => {
    LogService.info('PreferencesOnboarding', 'handleComplete called but is now deprecated');
    // No longer doing anything here - navigation is handled by the parent component
  };

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Main content - pass onValidationChange to SmartPreferencesWizard */}
          {React.cloneElement(
            children,
            {
              onValidationChange: setArePreferencesValid,
              ...children.props
            }
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Onboarding navigation removed as requested */}

      {/* Exit confirmation dialog */}
      <ValidationFeedback
        visible={showExitConfirmation}
        title="Exit Preferences Setup?"
        message="Your preferences are not complete. If you exit now, your progress may not be saved. Are you sure you want to exit?"
        onConfirm={handleExitConfirm}
        onCancel={handleExitCancel}
        confirmText="Exit"
        cancelText="Stay"
        isWarning={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.dark,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 20, // Reduced padding since we removed the navigation bar
  },
  navigationContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(10, 10, 24, 0.95)',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    zIndex: 10, // Ensure this is above other elements
  }
});
import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { PropertyMatch } from '../../store/aiStore';
import { listingsService } from '../../services/listingsService';
import { AIMatchScoreIndicator } from './AIMatchScoreIndicator';
import { PropertyFeaturesList } from './PropertyFeaturesList';
import { AISummarySection } from './AISummarySection';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

const { width } = Dimensions.get('window');

interface PropertyMatchDetailsProps {
  match: PropertyMatch;
  onApply: () => void;
  onSave: (isSaved: boolean) => void;
  onClose: () => void;
}

export const PropertyMatchDetails: React.FC<PropertyMatchDetailsProps> = ({
  match,
  onApply,
  onSave,
  onClose,
}) => {
  // State
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoading, setImageLoading] = useState(true);
  
  // Animation values
  const contentOpacity = useSharedValue(0);
  
  // Start animations when component mounts
  React.useEffect(() => {
    contentOpacity.value = withTiming(1, { 
      duration: 500, 
      easing: Easing.out(Easing.ease) 
    });
  }, []);
  
  // Handle save toggle
  const handleSaveToggle = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSave(match.saved);
  };
  
  // Handle apply
  const handleApply = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onApply();
  };
  
  // Handle close
  const handleClose = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
  };
  
  // Handle image navigation
  const nextImage = () => {
    if (match.listing.images && currentImageIndex < match.listing.images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
      setImageLoading(true);
    }
  };
  
  const prevImage = () => {
    if (match.listing.images && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
      setImageLoading(true);
    }
  };
  
  // Format location
  const formatLocation = () => {
    if (typeof match.listing.location === 'string') {
      return match.listing.location;
    } else if (typeof match.listing.location === 'object') {
      return match.listing.location.city || match.listing.location.address || 'Unknown location';
    }
    return 'Unknown location';
  };
  
  // Get current image or placeholder
  const getCurrentImage = () => {
    if (match.listing.images && match.listing.images.length > 0) {
      return { uri: match.listing.images[currentImageIndex] };
    }
    return require('../../assets/images/property-placeholder.png');
  };
  
  // Animated styles
  const contentAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: contentOpacity.value,
    };
  });
  
  return (
    <ScrollView 
      style={styles.container}
      showsVerticalScrollIndicator={false}
    >
      {/* Image Gallery */}
      <View style={styles.imageContainer}>
        <Image
          source={getCurrentImage()}
          style={styles.image}
          onLoadStart={() => setImageLoading(true)}
          onLoadEnd={() => setImageLoading(false)}
        />
        
        {imageLoading && (
          <View style={styles.imagePlaceholder}>
            <ActivityIndicator color={THEME.light} />
          </View>
        )}
        
        {/* Image Navigation */}
        {match.listing.images && match.listing.images.length > 1 && (
          <>
            <TouchableOpacity 
              style={[styles.imageNavButton, styles.prevButton]}
              onPress={prevImage}
              disabled={currentImageIndex === 0}
            >
              <Ionicons 
                name="chevron-back" 
                size={24} 
                color={currentImageIndex === 0 ? 'rgba(255,255,255,0.5)' : THEME.light} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.imageNavButton, styles.nextButton]}
              onPress={nextImage}
              disabled={currentImageIndex === match.listing.images.length - 1}
            >
              <Ionicons 
                name="chevron-forward" 
                size={24} 
                color={currentImageIndex === match.listing.images.length - 1 ? 'rgba(255,255,255,0.5)' : THEME.light} 
              />
            </TouchableOpacity>
            
            <View style={styles.imageCounter}>
              <Text style={styles.imageCounterText}>
                {currentImageIndex + 1} / {match.listing.images.length}
              </Text>
            </View>
          </>
        )}
        
        {/* Close Button */}
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={handleClose}
        >
          <Ionicons name="close" size={24} color={THEME.light} />
        </TouchableOpacity>
        
        {/* Save Button */}
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSaveToggle}
        >
          <Ionicons 
            name={match.saved ? "heart" : "heart-outline"} 
            size={24} 
            color={match.saved ? THEME.accent : THEME.light} 
          />
        </TouchableOpacity>
      </View>
      
      <Animated.View style={[styles.content, contentAnimatedStyle]}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.priceRow}>
            <Text style={styles.price}>
              {listingsService.formatPrice(match.listing.price)}
            </Text>
            <AIMatchScoreIndicator score={match.score} />
          </View>
          
          <Text style={styles.title}>{match.listing.title}</Text>
          
          <View style={styles.locationContainer}>
            <Ionicons name="location" size={16} color={THEME.gray} />
            <Text style={styles.location}>{formatLocation()}</Text>
          </View>
        </View>
        
        {/* Property Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailsRow}>
            {match.listing.propertyType && (
              <View style={styles.detailItem}>
                <Ionicons name="home-outline" size={20} color={THEME.accent} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Type</Text>
                  <Text style={styles.detailValue}>
                    {listingsService.getPropertyTypeDisplayName(match.listing.propertyType)}
                  </Text>
                </View>
              </View>
            )}
            
            {match.listing.rooms && (
              <View style={styles.detailItem}>
                <Ionicons name="bed-outline" size={20} color={THEME.accent} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Rooms</Text>
                  <Text style={styles.detailValue}>
                    {match.listing.rooms}
                  </Text>
                </View>
              </View>
            )}
          </View>
          
          <View style={styles.detailsRow}>
            {match.listing.area && (
              <View style={styles.detailItem}>
                <Ionicons name="square-outline" size={20} color={THEME.accent} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Area</Text>
                  <Text style={styles.detailValue}>
                    {match.listing.area} m²
                  </Text>
                </View>
              </View>
            )}
            
            {match.listing.dateAvailable && (
              <View style={styles.detailItem}>
                <Ionicons name="calendar-outline" size={20} color={THEME.accent} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Available</Text>
                  <Text style={styles.detailValue}>
                    {new Date(match.listing.dateAvailable).toLocaleDateString()}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
        
        {/* AI Summary Section */}
        <AISummarySection 
          pros={match.pros}
          cons={match.cons}
          reasons={match.reasons}
        />
        
        {/* Property Features */}
        <PropertyFeaturesList listing={match.listing} />
        
        {/* Description */}
        {match.listing.description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{match.listing.description}</Text>
          </View>
        )}
        
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={styles.applyButton}
            onPress={handleApply}
          >
            <Ionicons name="paper-plane" size={20} color={THEME.light} />
            <Text style={styles.applyButtonText}>Apply Now</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.saveActionButton}
            onPress={handleSaveToggle}
          >
            <Ionicons 
              name={match.saved ? "heart" : "heart-outline"} 
              size={20} 
              color={match.saved ? THEME.light : THEME.secondary} 
            />
            <Text 
              style={[
                styles.saveButtonText,
                match.saved ? styles.savedButtonText : null
              ]}
            >
              {match.saved ? 'Saved' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Source and Date Added */}
        <View style={styles.metadataContainer}>
          <Text style={styles.metadataText}>
            Source: {match.listing.source}
          </Text>
          <Text style={styles.metadataText}>
            Added: {new Date(match.listing.dateAdded).toLocaleDateString()}
          </Text>
        </View>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  imageContainer: {
    height: 300,
    width: '100%',
    position: 'relative',
  },
  image: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
  },
  imagePlaceholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    marginTop: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  prevButton: {
    left: 16,
  },
  nextButton: {
    right: 16,
  },
  imageCounter: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  imageCounterText: {
    color: THEME.light,
    fontSize: 12,
    fontWeight: '500',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 24,
  },
  header: {
    marginBottom: 24,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 24,
    fontWeight: '700',
    color: THEME.dark,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    fontSize: 16,
    color: THEME.gray,
    marginLeft: 4,
  },
  detailsContainer: {
    marginBottom: 24,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  detailItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailTextContainer: {
    marginLeft: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: THEME.gray,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.dark,
  },
  descriptionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: THEME.dark,
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  applyButton: {
    flex: 3,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.accent,
    paddingVertical: 16,
    borderRadius: 12,
    marginRight: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.light,
    marginLeft: 8,
  },
  saveActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: match => match.saved ? THEME.secondary : 'rgba(114, 9, 183, 0.1)',
    paddingVertical: 16,
    borderRadius: 12,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.secondary,
    marginLeft: 8,
  },
  savedButtonText: {
    color: THEME.light,
  },
  metadataContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metadataText: {
    fontSize: 12,
    color: THEME.gray,
  },
});
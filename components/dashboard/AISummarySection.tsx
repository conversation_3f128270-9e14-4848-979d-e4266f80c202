import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface AISummarySectionProps {
  pros: string[];
  cons: string[];
  reasons: string[];
}

export const AISummarySection: React.FC<AISummarySectionProps> = ({
  pros,
  cons,
  reasons,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<'highlights' | 'considerations' | 'reasons'>('highlights');
  
  // Animation values
  const tabAnimation = useSharedValue(0);
  
  // Update animation when tab changes
  React.useEffect(() => {
    let targetValue: number;
    
    switch (activeTab) {
      case 'highlights':
        targetValue = 0;
        break;
      case 'considerations':
        targetValue = 1;
        break;
      case 'reasons':
        targetValue = 2;
        break;
      default:
        targetValue = 0;
    }
    
    tabAnimation.value = withTiming(targetValue, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [activeTab]);
  
  // Handle tab change
  const handleTabChange = (tab: 'highlights' | 'considerations' | 'reasons') => {
    setActiveTab(tab);
  };
  
  // Animated styles
  const indicatorAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: interpolate(tabAnimation.value, [0, 1, 2], [0, 100, 200]) }
      ],
    };
  });
  
  // Get content based on active tab
  const getContent = () => {
    switch (activeTab) {
      case 'highlights':
        return pros.map((pro, index) => (
          <View key={`pro-${index}`} style={styles.contentItem}>
            <Ionicons name="checkmark-circle" size={16} color="#10b981" />
            <Text style={styles.contentText}>{pro}</Text>
          </View>
        ));
      case 'considerations':
        return cons.map((con, index) => (
          <View key={`con-${index}`} style={styles.contentItem}>
            <Ionicons name="alert-circle" size={16} color="#f59e0b" />
            <Text style={styles.contentText}>{con}</Text>
          </View>
        ));
      case 'reasons':
        return reasons.map((reason, index) => (
          <View key={`reason-${index}`} style={styles.contentItem}>
            <Ionicons name="information-circle" size={16} color={THEME.primary} />
            <Text style={styles.contentText}>{reason}</Text>
          </View>
        ));
      default:
        return null;
    }
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>AI Insights</Text>
      
      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <View style={styles.tabsBackground}>
          <Animated.View 
            style={[styles.tabIndicator, indicatorAnimatedStyle]} 
          />
        </View>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'highlights' ? styles.activeTab : null
          ]}
          onPress={() => handleTabChange('highlights')}
        >
          <Text 
            style={[
              styles.tabText,
              activeTab === 'highlights' ? styles.activeTabText : null
            ]}
          >
            Highlights
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'considerations' ? styles.activeTab : null
          ]}
          onPress={() => handleTabChange('considerations')}
        >
          <Text 
            style={[
              styles.tabText,
              activeTab === 'considerations' ? styles.activeTabText : null
            ]}
          >
            Considerations
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'reasons' ? styles.activeTab : null
          ]}
          onPress={() => handleTabChange('reasons')}
        >
          <Text 
            style={[
              styles.tabText,
              activeTab === 'reasons' ? styles.activeTabText : null
            ]}
          >
            Match Reasons
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Content */}
      <View style={styles.contentContainer}>
        {getContent()}
      </View>
      
      {/* AI Badge */}
      <View style={styles.aiBadge}>
        <Ionicons name="flash" size={12} color={THEME.accent} />
        <Text style={styles.aiBadgeText}>AI-powered insights</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    position: 'relative',
    height: 40,
  },
  tabsBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
  },
  tabIndicator: {
    position: 'absolute',
    width: '33.333%',
    height: '100%',
    backgroundColor: THEME.light,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  activeTab: {},
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  activeTabText: {
    color: THEME.dark,
  },
  contentContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
  },
  contentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  contentText: {
    flex: 1,
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 8,
    lineHeight: 20,
  },
  aiBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginTop: 12,
  },
  aiBadgeText: {
    fontSize: 12,
    color: THEME.accent,
    fontWeight: '500',
    marginLeft: 4,
  },
});
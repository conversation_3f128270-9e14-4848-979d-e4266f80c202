import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useAuthStore } from '../../store/authStore';
import { useAIStore } from '../../store/aiStore';
import { useNotificationStore } from '../../store/notificationStore';
import { PropertyMatchCard } from './PropertyMatchCard';
import { DashboardMetricsCard } from './DashboardMetricsCard';
import { MarketInsightCard } from './MarketInsightCard';
import { EmptyStateCard } from './EmptyStateCard';
import { RealTimeUpdateStatus } from './RealTimeUpdateStatus';
import { useRealTimeUpdates } from '../../hooks/useRealTimeUpdates';
import { registerBackgroundTasks } from '../../services/backgroundTaskService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

const { width } = Dimensions.get('window');

interface AIDashboardScreenProps {
  onUpdatePreferences?: () => void;
  onViewApplications?: () => void;
}

export const AIDashboardScreen: React.FC<AIDashboardScreenProps> = ({
  onUpdatePreferences,
  onViewApplications,
}) => {
  const router = useRouter();
  const { user } = useAuthStore();
  const { 
    matches, 
    matchingInProgress, 
    lastMatchUpdate,
    applications,
    marketInsights,
    requestPropertyMatching,
    refreshMatches,
    getMarketInsights,
    markMatchViewed
  } = useAIStore();
  
  // Get real-time updates hook
  const {
    isActive: realTimeUpdatesActive,
    refreshNow,
    startUpdates,
    stopUpdates,
    backgroundTasksAvailable,
    setupBackgroundTasks,
    learnFromInteractions
  } = useRealTimeUpdates(true); // Auto-start real-time updates
  
  // State
  const [refreshing, setRefreshing] = useState(false);
  const [greeting, setGreeting] = useState('');
  const [currentInsightIndex, setCurrentInsightIndex] = useState(0);
  const [showUpdateStatus, setShowUpdateStatus] = useState(false);
  
  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentOpacity = useSharedValue(0);
  
  // Generate appropriate greeting based on time of day
  useEffect(() => {
    const hour = new Date().getHours();
    let newGreeting = '';
    
    if (hour < 12) {
      newGreeting = 'Good morning';
    } else if (hour < 18) {
      newGreeting = 'Good afternoon';
    } else {
      newGreeting = 'Good evening';
    }
    
    if (user?.firstName) {
      newGreeting += `, ${user.firstName}`;
    }
    
    setGreeting(newGreeting);
  }, [user]);
  
  // Start animations when component mounts
  useEffect(() => {
    headerOpacity.value = withTiming(1, { 
      duration: 600, 
      easing: Easing.out(Easing.ease) 
    });
    
    contentOpacity.value = withTiming(1, { 
      duration: 800, 
      easing: Easing.out(Easing.ease) 
    });
  }, []);
  
  // Load initial data and setup background tasks
  useEffect(() => {
    if (user?.preferences) {
      // Only fetch if we don't have matches yet or if they're stale
      if (matches.length === 0 || !lastMatchUpdate) {
        requestPropertyMatching(user.preferences, user);
      }
      
      // Get market insights for the first preferred location
      if (user.preferences.preferredLocations?.[0] && marketInsights.length === 0) {
        getMarketInsights(user.preferences.preferredLocations[0], user.preferences);
      }
      
      // Setup background tasks for notifications and updates
      setupBackgroundTasks();
      
      // Initialize notifications
      useNotificationStore.getState().initializeNotifications();
      
      // Learn from user interactions
      learnFromInteractions();
    }
  }, [user]);
  
  // Handle refresh
  const onRefresh = useCallback(async () => {
    if (!user?.preferences) return;
    
    setRefreshing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      // Use the real-time update service to refresh
      await refreshNow();
      
      // Refresh market insights
      if (user.preferences.preferredLocations?.[0]) {
        await getMarketInsights(user.preferences.preferredLocations[0], user.preferences);
      }
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  }, [user, refreshNow]);
  
  // Handle view property details
  const handleViewPropertyDetails = (matchId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Mark match as viewed
    markMatchViewed(matchId);
    
    // Navigate to property details
    router.push(`/property/${matchId}`);
  };
  
  // Handle quick apply
  const handleQuickApply = (matchId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Mark match as viewed
    markMatchViewed(matchId);
    
    // Navigate to application screen
    router.push(`/apply/${matchId}`);
  };
  
  // Handle save property
  const handleSaveProperty = (matchId: string, isSaved: boolean) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (isSaved) {
      useAIStore.getState().unsaveMatch(matchId);
    } else {
      useAIStore.getState().saveMatch(matchId);
    }
  };
  
  // Handle update preferences
  const handleUpdatePreferences = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (onUpdatePreferences) {
      onUpdatePreferences();
    }
  };
  
  // Handle view applications
  const handleViewApplications = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (onViewApplications) {
      onViewApplications();
    }
  };
  
  // Toggle real-time updates
  const toggleRealTimeUpdates = (value: boolean) => {
    if (value) {
      startUpdates();
    } else {
      stopUpdates();
    }
  };
  
  // Toggle update status visibility
  const toggleUpdateStatus = () => {
    setShowUpdateStatus(!showUpdateStatus);
  };
  
  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
      transform: [
        { translateY: (1 - headerOpacity.value) * -20 }
      ]
    };
  });
  
  const contentAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: contentOpacity.value,
      transform: [
        { translateY: (1 - contentOpacity.value) * 20 }
      ]
    };
  });
  
  // Calculate metrics
  const savedCount = matches.filter(match => match.saved).length;
  const viewedCount = matches.filter(match => match.viewed).length;
  const applicationCount = applications.length;
  const pendingApplications = applications.filter(app => app.status === 'submitted').length;
  
  // Get current market insight
  const currentInsight = marketInsights.length > 0 ? marketInsights[0] : null;
  
  // Render empty state if no matches
  if (matches.length === 0 && !matchingInProgress) {
    return (
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <Animated.View style={[styles.header, headerAnimatedStyle]}>
          <Text style={styles.greeting}>{greeting}</Text>
          <Text style={styles.subtitle}>Let's find your perfect home</Text>
          
          <View style={styles.updateToggleContainer}>
            <RealTimeUpdateStatus compact={true} showControls={false} />
          </View>
        </Animated.View>
        
        <Animated.View style={[styles.content, contentAnimatedStyle]}>
          <EmptyStateCard 
            title="No matches yet"
            message="We're looking for properties that match your preferences. Update your preferences or check back soon."
            icon="home-outline"
            actionLabel="Update Preferences"
            onAction={handleUpdatePreferences}
          />
          
          {currentInsight && (
            <MarketInsightCard 
              insight={currentInsight}
              onUpdatePreferences={handleUpdatePreferences}
            />
          )}
          
          {/* Real-time update status */}
          {showUpdateStatus && (
            <RealTimeUpdateStatus />
          )}
          
          {/* Update status toggle */}
          <TouchableOpacity 
            style={styles.updateStatusToggle}
            onPress={toggleUpdateStatus}
          >
            <Text style={styles.updateStatusToggleText}>
              {showUpdateStatus ? 'Hide update settings' : 'Show update settings'}
            </Text>
            <Ionicons 
              name={showUpdateStatus ? 'chevron-up' : 'chevron-down'} 
              size={16} 
              color={THEME.primary} 
            />
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    );
  }
  
  return (
    <ScrollView 
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <Text style={styles.greeting}>{greeting}</Text>
        <Text style={styles.subtitle}>Here's what we found for you</Text>
        
        <View style={styles.updateToggleContainer}>
          <RealTimeUpdateStatus compact={true} showControls={false} />
        </View>
      </Animated.View>
      
      <Animated.View style={[styles.content, contentAnimatedStyle]}>
        {/* Metrics Card */}
        <DashboardMetricsCard 
          matches={matches.length}
          saved={savedCount}
          viewed={viewedCount}
          applications={applicationCount}
          pending={pendingApplications}
          onViewApplications={handleViewApplications}
        />
        
        {/* Market Insight Card */}
        {currentInsight && (
          <MarketInsightCard 
            insight={currentInsight}
            onUpdatePreferences={handleUpdatePreferences}
          />
        )}
        
        {/* Real-time update status */}
        {showUpdateStatus && (
          <RealTimeUpdateStatus />
        )}
        
        {/* Top Matches Section */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Top Matches</Text>
          <TouchableOpacity onPress={() => router.push('/matches')}>
            <Text style={styles.seeAllText}>See all</Text>
          </TouchableOpacity>
        </View>
        
        {/* Property Match Cards */}
        {matchingInProgress && matches.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={THEME.accent} />
            <Text style={styles.loadingText}>Finding your perfect matches...</Text>
          </View>
        ) : (
          matches.slice(0, 5).map((match) => (
            <PropertyMatchCard
              key={match.id}
              match={match}
              onViewDetails={() => handleViewPropertyDetails(match.id)}
              onQuickApply={() => handleQuickApply(match.id)}
              onSave={(isSaved) => handleSaveProperty(match.id, isSaved)}
            />
          ))
        )}
        
        {/* Update status toggle */}
        <TouchableOpacity 
          style={styles.updateStatusToggle}
          onPress={toggleUpdateStatus}
        >
          <Text style={styles.updateStatusToggleText}>
            {showUpdateStatus ? 'Hide update settings' : 'Show update settings'}
          </Text>
          <Ionicons 
            name={showUpdateStatus ? 'chevron-up' : 'chevron-down'} 
            size={16} 
            color={THEME.primary} 
          />
        </TouchableOpacity>
        
        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={handleUpdatePreferences}
          >
            <Ionicons name="options-outline" size={24} color={THEME.light} />
            <Text style={styles.quickActionText}>Update Preferences</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.quickActionButton, styles.secondaryActionButton]}
            onPress={handleViewApplications}
          >
            <Ionicons name="document-text-outline" size={24} color={THEME.light} />
            <Text style={styles.quickActionText}>View Applications</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  contentContainer: {
    paddingBottom: 40,
  },
  header: {
    padding: 24,
    backgroundColor: THEME.dark,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: THEME.light,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 8,
  },
  updateToggleContainer: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  content: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
  },
  seeAllText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
    textAlign: 'center',
  },
  updateStatusToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    marginBottom: 8,
  },
  updateStatusToggleText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.accent,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryActionButton: {
    backgroundColor: THEME.secondary,
  },
  quickActionText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
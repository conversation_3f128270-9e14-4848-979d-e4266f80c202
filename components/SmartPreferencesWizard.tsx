import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  Easing,
  SlideInRight,
  SlideOutLeft,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { UserPreferences } from "../services/authService";
import { LogService } from "../services/logService";
import { PreferencesValidationService } from "../services/preferencesValidationService";
import { useAIStore } from "../store/aiStore";
import { useAuthStore } from "../store/authStore";
import { PreferencesSaveButton } from "./preferences/PreferencesSaveButton";
import { StepValidationFeedback } from "./preferences/StepValidationFeedback";

// Import wizard step components
import { AmenitiesStep } from "./preferences/AmenitiesStep";
import { BudgetStep } from "./preferences/BudgetStep";
import { LocationStep } from "./preferences/LocationStep";
import { NotificationStep } from "./preferences/NotificationStep";
import { ProfileTypeStep } from "./preferences/ProfileTypeStep";
import { PropertyTypeStep } from "./preferences/PropertyTypeStep";
import { SummaryStep } from "./preferences/SummaryStep";
// Fallback in case the service is not properly loaded
const validatePreferences = (preferences: any) => {
  if (
    PreferencesValidationService &&
    typeof PreferencesValidationService.validatePreferences === "function"
  ) {
    return PreferencesValidationService.validatePreferences(preferences);
  } else {
    // Fallback validation
    return {
      isValid: true,
      missingFields: [],
      message: "Validation service not available, assuming valid",
    };
  }
};

const { width } = Dimensions.get("window");

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
};

// Define wizard steps
export const WIZARD_STEPS = [
  {
    id: "profile",
    title: "Your Profile",
    description: "Tell us about yourself so we can personalize your experience",
    icon: "person-outline",
  },
  {
    id: "location",
    title: "Location",
    description: "Where would you like to live?",
    icon: "location-outline",
  },
  {
    id: "budget",
    title: "Budget",
    description: "What's your rental budget?",
    icon: "cash-outline",
  },
  {
    id: "property",
    title: "Property Type",
    description: "What kind of property are you looking for?",
    icon: "home-outline",
  },
  {
    id: "amenities",
    title: "Amenities",
    description: "Select your preferred amenities",
    icon: "list-outline",
  },
  {
    id: "notifications",
    title: "Notifications",
    description: "How would you like to be notified?",
    icon: "notifications-outline",
  },
  {
    id: "summary",
    title: "Summary",
    description: "Review your preferences",
    icon: "checkmark-circle-outline",
  },
];

// Draft persistence for wizard revisit
const PREFERENCES_WIZARD_DRAFT_KEY = "preferences-wizard-draft";

function isPreferencesEmpty(p?: Partial<UserPreferences> | null): boolean {
  if (!p) return true;
  const {
    minPrice,
    maxPrice,
    preferredLocations,
    propertyTypes,
    minRooms,
    maxRooms,
    amenities,
    notifications,
  } = p;
  return (
    !minPrice &&
    !maxPrice &&
    (!preferredLocations || preferredLocations.length === 0) &&
    (!propertyTypes || propertyTypes.length === 0) &&
    !minRooms &&
    !maxRooms &&
    (!amenities || amenities.length === 0) &&
    !notifications
  );
}

async function loadWizardDraft(): Promise<{
  preferences?: Partial<UserPreferences>;
  step?: number;
} | null> {
  try {
    const raw = await AsyncStorage.getItem(PREFERENCES_WIZARD_DRAFT_KEY);
    if (!raw) return null;
    return JSON.parse(raw);
  } catch {
    return null;
  }
}

async function saveWizardDraft(data: {
  preferences: Partial<UserPreferences>;
  step: number;
}) {
  try {
    await AsyncStorage.setItem(
      PREFERENCES_WIZARD_DRAFT_KEY,
      JSON.stringify({ ...data, updatedAt: Date.now() })
    );
  } catch {}
}

async function clearWizardDraft() {
  try {
    await AsyncStorage.removeItem(PREFERENCES_WIZARD_DRAFT_KEY);
  } catch {}
}

// Default preferences based on user profile
export const DEFAULT_PREFERENCES: Record<string, Partial<UserPreferences>> = {
  student: {
    minPrice: 500,
    maxPrice: 1200,
    minRooms: 1,
    maxRooms: 2,
    propertyTypes: ["apartment", "room"],
    amenities: ["internet", "washer"],
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
  },
  expat: {
    minPrice: 1000,
    maxPrice: 2000,
    minRooms: 1,
    maxRooms: 3,
    propertyTypes: ["apartment", "house"],
    amenities: ["furnished", "parking", "balcony"],
    notifications: {
      email: true,
      push: true,
      sms: true,
    },
  },
  professional: {
    minPrice: 1200,
    maxPrice: 2500,
    minRooms: 2,
    maxRooms: 4,
    propertyTypes: ["apartment", "house"],
    amenities: ["parking", "balcony", "garden"],
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
  },
};

interface SmartPreferencesWizardProps {
  onComplete: (preferences: UserPreferences) => void;
  initialPreferences?: Partial<UserPreferences>;
  onValidationChange?: (isValid: boolean) => void;
}

export const SmartPreferencesWizard: React.FC<SmartPreferencesWizardProps> = ({
  onComplete,
  initialPreferences,
  onValidationChange,
}) => {
  const router = useRouter();
  const { user, updatePreferences } = useAuthStore();
  const { getMarketInsights, requestPropertyMatching } = useAIStore();

  // State
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [preferences, setPreferences] = useState<Partial<UserPreferences>>(
    initialPreferences || user?.preferences || {}
  );
  const [userProfile, setUserProfile] = useState<
    "student" | "expat" | "professional" | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);
  const [marketData, setMarketData] = useState<any>(null);

  // On mount: seed from stored draft or initial/user preferences
  useEffect(() => {
    let cancelled = false;
    (async () => {
      const incoming = initialPreferences || user?.preferences || {};
      const draft = await loadWizardDraft();
      if (cancelled) return;
      if (
        draft &&
        draft.preferences &&
        !isPreferencesEmpty(draft.preferences)
      ) {
        setPreferences((prev) => ({ ...prev, ...draft.preferences }));
        if (typeof draft.step === "number") {
          setCurrentStepIndex(
            Math.max(0, Math.min(WIZARD_STEPS.length - 1, draft.step))
          );
        }
      } else if (!isPreferencesEmpty(incoming)) {
        setPreferences((prev) => ({ ...prev, ...incoming }));
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [initialPreferences, user?.preferences]);

  // Animation values
  const slideAnimation = useSharedValue(0);
  const progressValue = useSharedValue(0);

  // Update progress animation when step changes
  useEffect(() => {
    const progress = (currentStepIndex + 1) / WIZARD_STEPS.length;
    progressValue.value = withTiming(progress, {
      duration: 400,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    slideAnimation.value = withTiming(1, {
      duration: 300,
      easing: Easing.out(Easing.ease),
    });

    return () => {
      slideAnimation.value = 0;
    };
  }, [currentStepIndex]);

  // Apply default preferences when user profile changes
  useEffect(() => {
    if (userProfile) {
      const defaults = DEFAULT_PREFERENCES[userProfile];
      setPreferences((prev) => ({
        ...prev,
        ...defaults,
      }));

      // Fetch market insights for default location if available
      if (
        defaults.preferredLocations &&
        defaults.preferredLocations.length > 0
      ) {
        fetchMarketInsights(defaults.preferredLocations[0]);
      }
    }
  }, [userProfile]);

  // Fetch market insights for a location
  const fetchMarketInsights = async (location: string) => {
    try {
      setIsLoading(true);

      // Create a complete UserPreferences object with default values for any undefined properties
      const completePreferences: UserPreferences = {
        minPrice: preferences.minPrice || 0,
        maxPrice: preferences.maxPrice || 2000,
        preferredLocations: preferences.preferredLocations || [],
        propertyTypes: preferences.propertyTypes || ["apartment"],
        minRooms: preferences.minRooms || 1,
        maxRooms: preferences.maxRooms || 3,
        amenities: preferences.amenities || [],
        notifications: preferences.notifications || {
          email: true,
          push: true,
          sms: false,
        },
      };

      const insights = await getMarketInsights(location, completePreferences);
      setMarketData(insights);
    } catch (error) {
      console.error("Failed to fetch market insights:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle location market data from LocationStep
  const handleLocationMarketData = (location: string, data: any) => {
    // Update market data with the most recent location data
    setMarketData(data);
  };

  // Update preferences
  const updatePreferencesData = (data: Partial<UserPreferences>) => {
    setPreferences((prev) => {
      const merged = { ...prev, ...data };
      // Persist draft on each change
      saveWizardDraft({ preferences: merged, step: currentStepIndex });
      return merged;
    });

    // If location changes, fetch new market insights
    if (
      data.preferredLocations &&
      data.preferredLocations[0] !== preferences.preferredLocations?.[0]
    ) {
      fetchMarketInsights(data.preferredLocations[0]);
    }
  };

  // Navigation handlers
  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (currentStepIndex < WIZARD_STEPS.length - 1) {
      const next = currentStepIndex + 1;
      setCurrentStepIndex(next);
      // Persist draft after step change
      saveWizardDraft({ preferences: { ...preferences }, step: next });
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  // State for validation feedback
  const [validationMessage, setValidationMessage] = useState<{
    isValid: boolean;
    message: string;
  } | null>(null);

  const handleComplete = async () => {
    try {
      setIsLoading(true);

      // Ensure all required fields are present
      const completePreferences: UserPreferences = {
        minPrice: preferences.minPrice || 0,
        maxPrice: preferences.maxPrice || 2000,
        preferredLocations: preferences.preferredLocations || [],
        propertyTypes: preferences.propertyTypes || ["apartment"],
        minRooms: preferences.minRooms || 1,
        maxRooms: preferences.maxRooms || 3,
        amenities: preferences.amenities || [],
        notifications: preferences.notifications || {
          email: true,
          push: true,
          sms: false,
        },
      };

      // Validate preferences before proceeding
      const validationResult =
        PreferencesValidationService.validatePreferences(completePreferences);

      // Notify parent component about validation status
      if (onValidationChange) {
        onValidationChange(validationResult.isValid);
      }

      if (!validationResult.isValid) {
        // Show validation error
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        setValidationMessage({
          isValid: false,
          message: `Please complete all required preferences: ${validationResult.missingFields.join(
            ", "
          )}`,
        });

        // If we're not on the summary step, go there to show what's missing
        if (currentStepIndex !== WIZARD_STEPS.length - 1) {
          setCurrentStepIndex(WIZARD_STEPS.length - 1);
        }

        return;
      }

      // Clear any validation messages
      setValidationMessage(null);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Show success message
      setValidationMessage({
        isValid: true,
        message: "Preferences saved successfully! Redirecting to dashboard...",
      });

      // Persist preferences to backend and refresh cached user BEFORE completing
      try {
        // 1) Save via store (calls authService.updatePreferences under the hood)
        await updatePreferences(completePreferences);

        // 2) Force-refresh cached user so downstream fetch uses fresh preferences
        try {
          const { authService } = await import("../services/authService");
          // Refresh preferences cache in background; do not block completion
          authService
            .getPreferences()
            .catch((e) =>
              LogService.warn(
                "PreferencesWizard",
                "Non-fatal: refresh preferences cache failed",
                e as any
              )
            );
        } catch (refreshErr) {
          // Non-fatal: log only
          LogService.warn(
            "PreferencesWizard",
            "Failed to refresh preferences cache",
            refreshErr as any
          );
        }

        // 3) Proactively trigger preference-based listings to avoid fallback on dashboard
        try {
          const { useListingsStore } = await import("../store/listingsStore");
          const fetchPref =
            useListingsStore.getState().fetchPreferenceBasedListings;
          if (typeof fetchPref === "function") {
            // Prefetch in background; never block completion
            fetchPref(10).catch((e: any) =>
              LogService.warn(
                "PreferencesWizard",
                "Non-fatal: prefetch preference-based listings failed",
                e
              )
            );
          }
        } catch (prefErr) {
          // Non-fatal: log only
          LogService.warn(
            "PreferencesWizard",
            "Failed to prefetch preference-based listings",
            prefErr as any
          );
        }
      } catch (persistErr) {
        LogService.error(
          "PreferencesWizard",
          "Failed to persist preferences before completion",
          persistErr as any
        );
      }

      // Preferences successfully saved; clear draft so revisit shows saved settings
      await clearWizardDraft();

      // Call the onComplete callback immediately after saving
      // Pass a flag to indicate preferences are already saved
      onComplete(completePreferences);
    } catch (error) {
      // Only log critical errors
      LogService.error("PreferencesWizard", "Exception in handleComplete", {
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });

      setValidationMessage({
        isValid: false,
        message: "Failed to save preferences. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Skip to next step
  const skipToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < WIZARD_STEPS.length) {
      setCurrentStepIndex(stepIndex);
    }
  };

  // Animated styles
  const progressAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: `${progressValue.value * 100}%`,
    };
  });

  // Get current step
  const currentStep = WIZARD_STEPS[currentStepIndex];
  const isLastStep = currentStepIndex === WIZARD_STEPS.length - 1;

  // Render step content
  const renderStepContent = () => {
    switch (currentStep.id) {
      case "profile":
        return (
          <ProfileTypeStep
            selectedProfile={userProfile}
            onSelectProfile={setUserProfile}
          />
        );
      case "location":
        return (
          <LocationStep
            selectedLocations={preferences.preferredLocations || []}
            onSelectLocations={(locations) =>
              updatePreferencesData({ preferredLocations: locations })
            }
            isLoading={isLoading}
            onLocationMarketData={handleLocationMarketData}
          />
        );
      case "budget":
        return (
          <BudgetStep
            minPrice={preferences.minPrice || 0}
            maxPrice={preferences.maxPrice || 2000}
            onUpdateBudget={(min, max) =>
              updatePreferencesData({ minPrice: min, maxPrice: max })
            }
            marketData={marketData}
            userProfile={userProfile}
            selectedLocations={preferences.preferredLocations || []}
          />
        );
      case "property":
        return (
          <PropertyTypeStep
            selectedTypes={preferences.propertyTypes || []}
            minRooms={preferences.minRooms || 1}
            maxRooms={preferences.maxRooms || 3}
            onUpdatePropertyPreferences={(types, minRooms, maxRooms) =>
              updatePreferencesData({
                propertyTypes: types,
                minRooms,
                maxRooms,
              })
            }
          />
        );
      case "amenities":
        return (
          <AmenitiesStep
            selectedAmenities={preferences.amenities || []}
            onUpdateAmenities={(amenities) =>
              updatePreferencesData({ amenities })
            }
          />
        );
      case "notifications":
        return (
          <NotificationStep
            notificationSettings={
              preferences.notifications || {
                email: true,
                push: true,
                sms: false,
              }
            }
            onUpdateNotifications={(notifications) =>
              updatePreferencesData({ notifications })
            }
          />
        );
      case "summary":
        return (
          <SummaryStep
            preferences={preferences as UserPreferences}
            onEditSection={(sectionIndex) => skipToStep(sectionIndex)}
            marketData={marketData}
            // Do not pre-save here; SmartPreferencesWizard.handleComplete will persist once
            onComplete={handleComplete}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBarContainer}>
          <Animated.View style={[styles.progressBar, progressAnimatedStyle]} />
        </View>
        <Text style={styles.progressText}>
          Step {currentStepIndex + 1} of {WIZARD_STEPS.length}
        </Text>
      </View>

      {/* Step header */}
      <View style={styles.stepHeader}>
        <View style={styles.stepIconContainer}>
          <Ionicons
            name={currentStep.icon as any}
            size={24}
            color={THEME.accent}
          />
        </View>
        <View style={styles.stepTitleContainer}>
          <Text style={styles.stepTitle}>{currentStep.title}</Text>
          <Text style={styles.stepDescription}>{currentStep.description}</Text>
        </View>
      </View>

      {/* Step content */}
      <ScrollView
        style={styles.contentContainer}
        contentContainerStyle={[
          styles.contentScrollContainer,
          isLastStep && styles.lastStepContentContainer,
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Validation message */}
        {validationMessage && (
          <StepValidationFeedback
            isValid={validationMessage.isValid}
            message={validationMessage.message}
          />
        )}

        <Animated.View
          key={currentStepIndex}
          entering={SlideInRight.duration(300)}
          exiting={SlideOutLeft.duration(300)}
          style={styles.stepContent}
        >
          {renderStepContent()}
        </Animated.View>
      </ScrollView>

      {/* Navigation buttons - hide on last step to avoid overlap with onboarding navigation */}
      {!isLastStep && (
        <View style={styles.navigationContainer}>
          {currentStepIndex > 0 && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
              disabled={isLoading}
            >
              <Ionicons name="arrow-back" size={20} color={THEME.gray} />
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}

          <PreferencesSaveButton
            onPress={handleNext}
            isLoading={isLoading}
            isLastStep={false}
            label="Next"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  progressContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  progressBarContainer: {
    flex: 1,
    height: 4,
    backgroundColor: "#e5e7eb",
    borderRadius: 0,
    overflow: "hidden",
    marginRight: 16,
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#f72585", // Bright pink/magenta color as seen in the screenshot
    borderRadius: 0,
  },
  progressText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6b7280", // Gray color for the step text
  },
  stepHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  stepIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(247, 37, 133, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  stepTitleContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: THEME.gray,
  },
  contentContainer: {
    flex: 1,
  },
  contentScrollContainer: {
    padding: 24,
    paddingBottom: 1, // Extra padding for navigation bars
  },
  stepContent: {
    minHeight: 300,
  },
  navigationContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
    backgroundColor: "#ffffff",
    position: "absolute",
    bottom: 2, // Moved up much more to avoid overlap with onboarding navigation
    left: 0,
    right: 0,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    zIndex: 5, // Lower than the onboarding navigation
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: THEME.gray,
    fontWeight: "500",
  },
  nextButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: THEME.accent,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    minWidth: 120,
  },
  completeButton: {
    backgroundColor: THEME.primary,
  },
  disabledButton: {
    opacity: 0.7,
  },
  nextButtonText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  lastStepContentContainer: {
    paddingBottom: 3, // Much more padding on last step to accommodate the onboarding navigation
  },
});

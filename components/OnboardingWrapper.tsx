import React from 'react';
import { StyleSheet, View, SafeAreaView, StatusBar } from 'react-native';
import { usePathname } from 'expo-router';
import { OnboardingNavigator, OnboardingStep } from './OnboardingNavigator';
import { OnboardingContextHelp } from './OnboardingContextHelp';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

// Define onboarding steps
const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome',
    route: '/',
    helpItems: [
      {
        title: 'AI-Powered Experience',
        content: 'ZakMakelaar uses advanced AI to find your perfect rental property and handle applications automatically.',
        icon: 'bulb-outline'
      },
      {
        title: 'Onboarding Process',
        content: 'The onboarding consists of 3 simple steps: welcome, authentication, and preferences setup.',
        icon: 'list-outline'
      },
      {
        title: 'Autonomous Features',
        content: 'After setup, our AI can automatically find and apply to properties that match your criteria.',
        icon: 'flash-outline'
      }
    ]
  },
  {
    id: 'auth',
    title: 'Authentication',
    route: '/login',
    helpItems: [
      {
        title: 'Secure Login',
        content: 'Your credentials are securely encrypted and never stored in plain text.',
        icon: 'shield-checkmark-outline'
      },
      {
        title: 'Account Benefits',
        content: 'Creating an account enables personalized AI matching and autonomous application features.',
        icon: 'person-add-outline'
      },
      {
        title: 'Data Privacy',
        content: 'We only collect information necessary for finding your ideal rental property.',
        icon: 'lock-closed-outline'
      },
      {
        title: 'Social Login',
        content: 'Coming soon: Quick login with Google and Apple accounts for faster access.',
        icon: 'logo-google'
      }
    ]
  },
  {
    id: 'preferences',
    title: 'Preferences',
    route: '/preferences',
    helpItems: [
      {
        title: 'AI Matching',
        content: 'The preferences you set will be used by our AI to find properties that perfectly match your needs.',
        icon: 'options-outline'
      },
      {
        title: 'Smart Defaults',
        content: 'We provide intelligent default values based on market data and popular choices.',
        icon: 'analytics-outline'
      },
      {
        title: 'Autonomous Mode',
        content: 'Your preferences determine which properties the AI will automatically apply for when enabled.',
        icon: 'rocket-outline'
      },
      {
        title: 'Updating Later',
        content: 'You can always update your preferences later from your profile settings.',
        icon: 'refresh-outline'
      }
    ]
  }
];

interface OnboardingWrapperProps {
  children: React.ReactNode;
  currentStepId: 'welcome' | 'auth' | 'preferences';
  allowSkip?: boolean;
  onComplete?: () => void;
}

export const OnboardingWrapper: React.FC<OnboardingWrapperProps> = ({
  children,
  currentStepId,
  allowSkip = false,
  onComplete
}) => {
  const pathname = usePathname();
  
  // Determine if we should show the onboarding navigation
  // Don't show on welcome screen as it has its own navigation
  const showOnboardingNav = currentStepId !== 'welcome';
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Main content */}
      <View style={styles.content}>
        {children}
      </View>
      
      {/* Onboarding navigation */}
      {showOnboardingNav && (
        <View style={styles.navigationContainer}>
          <OnboardingNavigator 
            steps={ONBOARDING_STEPS}
            currentStepId={currentStepId}
            allowSkip={allowSkip}
            onComplete={onComplete}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.dark,
  },
  content: {
    flex: 1,
  },
  navigationContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  }
});
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ContextualHelp } from './ContextualHelp';

interface OnboardingHelpItem {
  title: string;
  content: string;
  icon?: string;
}

interface OnboardingContextHelpProps {
  screenId: 'welcome' | 'auth' | 'preferences';
}

export const OnboardingContextHelp: React.FC<OnboardingContextHelpProps> = ({
  screenId
}) => {
  // Help content for different screens
  const helpContent: Record<string, { title: string; items: OnboardingHelpItem[] }> = {
    welcome: {
      title: 'Welcome',
      items: [
        {
          title: 'AI-Powered Experience',
          content: 'ZakMakelaar uses advanced AI to find your perfect rental property and handle applications automatically.',
          icon: 'bulb-outline'
        },
        {
          title: 'Onboarding Process',
          content: 'The onboarding consists of 3 simple steps: welcome, authentication, and preferences setup.',
          icon: 'list-outline'
        },
        {
          title: 'Autonomous Features',
          content: 'After setup, our AI can automatically find and apply to properties that match your criteria.',
          icon: 'flash-outline'
        }
      ]
    },
    auth: {
      title: 'Authentication',
      items: [
        {
          title: 'Secure Login',
          content: 'Your credentials are securely encrypted and never stored in plain text.',
          icon: 'shield-checkmark-outline'
        },
        {
          title: 'Account Benefits',
          content: 'Creating an account enables personalized AI matching and autonomous application features.',
          icon: 'person-add-outline'
        },
        {
          title: 'Data Privacy',
          content: 'We only collect information necessary for finding your ideal rental property.',
          icon: 'lock-closed-outline'
        },
        {
          title: 'Social Login',
          content: 'Coming soon: Quick login with Google and Apple accounts for faster access.',
          icon: 'logo-google'
        }
      ]
    },
    preferences: {
      title: 'Preferences',
      items: [
        {
          title: 'AI Matching',
          content: 'The preferences you set will be used by our AI to find properties that perfectly match your needs.',
          icon: 'options-outline'
        },
        {
          title: 'Smart Defaults',
          content: 'We provide intelligent default values based on market data and popular choices.',
          icon: 'analytics-outline'
        },
        {
          title: 'Autonomous Mode',
          content: 'Your preferences determine which properties the AI will automatically apply for when enabled.',
          icon: 'rocket-outline'
        },
        {
          title: 'Updating Later',
          content: 'You can always update your preferences later from your profile settings.',
          icon: 'refresh-outline'
        }
      ]
    }
  };
  
  const currentHelp = helpContent[screenId] || helpContent.welcome;
  
  return (
    <View style={styles.container}>
      <ContextualHelp 
        items={currentHelp.items}
        screenName={currentHelp.title}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // No specific styles needed
  }
});
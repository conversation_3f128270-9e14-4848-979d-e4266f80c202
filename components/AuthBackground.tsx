import React, { useEffect } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming, 
  Easing,
  interpolate,
  withDelay,
  withSequence
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { FuturisticGrid } from './FuturisticGrid';

const { width, height } = Dimensions.get('window');

interface AuthBackgroundProps {
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
}

export const AuthBackground: React.FC<AuthBackgroundProps> = ({
  primaryColor = '#4361ee',
  secondaryColor = '#7209b7',
  accentColor = '#f72585'
}) => {
  // Animation values
  const rotation1 = useSharedValue(0);
  const rotation2 = useSharedValue(0);
  const rotation3 = useSharedValue(0);
  const scale1 = useSharedValue(1);
  const scale2 = useSharedValue(0.8);
  const scale3 = useSharedValue(0.6);
  const translateX1 = useSharedValue(0);
  const translateY1 = useSharedValue(0);
  const translateX2 = useSharedValue(0);
  const translateY2 = useSharedValue(0);
  const opacity1 = useSharedValue(0.5);
  const opacity2 = useSharedValue(0.3);
  const opacity3 = useSharedValue(0.2);
  
  // Start animations
  useEffect(() => {
    // Rotation animations with different speeds
    rotation1.value = withRepeat(
      withTiming(360, { duration: 30000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    rotation2.value = withRepeat(
      withTiming(-360, { duration: 35000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    rotation3.value = withRepeat(
      withTiming(360, { duration: 40000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    // Scale animations with sequence for more dynamic effect
    scale1.value = withRepeat(
      withSequence(
        withTiming(1.2, { duration: 10000, easing: Easing.inOut(Easing.ease) }),
        withTiming(1.0, { duration: 10000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    scale2.value = withRepeat(
      withSequence(
        withTiming(0.9, { duration: 12000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.7, { duration: 12000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    scale3.value = withRepeat(
      withSequence(
        withTiming(0.7, { duration: 15000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.5, { duration: 15000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    // Translation animations for floating effect
    translateX1.value = withRepeat(
      withSequence(
        withTiming(width * 0.05, { duration: 20000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-width * 0.05, { duration: 20000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY1.value = withRepeat(
      withSequence(
        withTiming(-height * 0.02, { duration: 25000, easing: Easing.inOut(Easing.ease) }),
        withTiming(height * 0.02, { duration: 25000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateX2.value = withRepeat(
      withSequence(
        withTiming(-width * 0.07, { duration: 22000, easing: Easing.inOut(Easing.ease) }),
        withTiming(width * 0.07, { duration: 22000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY2.value = withRepeat(
      withSequence(
        withTiming(height * 0.03, { duration: 18000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-height * 0.03, { duration: 18000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Opacity animations
    opacity1.value = withRepeat(
      withSequence(
        withTiming(0.4, { duration: 8000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.6, { duration: 8000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    opacity2.value = withRepeat(
      withSequence(
        withTiming(0.2, { duration: 10000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.4, { duration: 10000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    opacity3.value = withRepeat(
      withSequence(
        withTiming(0.1, { duration: 12000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.3, { duration: 12000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
  }, []);
  
  // Animated styles
  const animatedStyle1 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX1.value },
        { translateY: translateY1.value },
        { rotate: `${rotation1.value}deg` },
        { scale: scale1.value }
      ],
      opacity: opacity1.value,
    };
  });
  
  const animatedStyle2 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX2.value },
        { translateY: translateY2.value },
        { rotate: `${rotation2.value}deg` },
        { scale: scale2.value }
      ],
      opacity: opacity2.value,
    };
  });
  
  const animatedStyle3 = useAnimatedStyle(() => {
    return {
      transform: [
        { rotate: `${rotation3.value}deg` },
        { scale: scale3.value }
      ],
      opacity: opacity3.value,
    };
  });

  return (
    <View style={styles.container}>
      {/* Main gradient background */}
      <LinearGradient
        colors={['#121212', '#1a1a2e']}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* Animated blobs with gradients */}
      <Animated.View style={[styles.blob1, animatedStyle1]}>
        <LinearGradient
          colors={[primaryColor, secondaryColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      <Animated.View style={[styles.blob2, animatedStyle2]}>
        <LinearGradient
          colors={[secondaryColor, accentColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      <Animated.View style={[styles.blob3, animatedStyle3]}>
        <LinearGradient
          colors={[accentColor, primaryColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      {/* Grid overlay for futuristic effect */}
      <FuturisticGrid cellSize={30} lineColor="rgba(73, 80, 170, 0.15)" />
      
      {/* Frosted glass effect overlay */}
      <View style={styles.overlay} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    overflow: 'hidden',
  },
  gradientBackground: {
    ...StyleSheet.absoluteFillObject,
  },
  blob1: {
    position: 'absolute',
    width: width * 1.2,
    height: width * 1.2,
    borderRadius: width * 0.6,
    top: -width * 0.6,
    right: -width * 0.3,
    overflow: 'hidden',
  },
  blob2: {
    position: 'absolute',
    width: width * 1.0,
    height: width * 1.0,
    borderRadius: width * 0.5,
    bottom: -width * 0.2,
    left: -width * 0.3,
    overflow: 'hidden',
  },
  blob3: {
    position: 'absolute',
    width: width * 0.8,
    height: width * 0.8,
    borderRadius: width * 0.4,
    top: height * 0.4,
    right: -width * 0.2,
    overflow: 'hidden',
  },
  blobGradient: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.92)',
  }
});
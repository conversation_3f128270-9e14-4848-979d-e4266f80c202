import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

type PropertyStatus = 'draft' | 'active' | 'rented' | 'maintenance' | 'inactive';

interface PropertyStatusBadgeProps {
  status: PropertyStatus;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
}

export const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({
  status,
  size = 'medium',
  showIcon = true,
}) => {

  const getSizeStyles = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
      case 'small':
        return {
          container: styles.containerSmall,
          text: styles.textSmall,
          icon: 12,
        };
      case 'large':
        return {
          container: styles.containerLarge,
          text: styles.textLarge,
          icon: 20,
        };
      default:
        return {
          container: styles.containerMedium,
          text: styles.textMedium,
          icon: 16,
        };
    }
  };

  const config = getStatusConfig(status);
  const sizeStyles = getSizeStyles(size);

  return (
    <View
      style={[
        styles.container,
        sizeStyles.container,
        {
          backgroundColor: config.backgroundColor,
        },
      ]}
    >
      {showIcon && (
        <Ionicons
          name={config.icon}
          size={sizeStyles.icon}
          color={config.color}
          style={styles.icon}
        />
      )}
      <Text
        style={[
          styles.text,
          sizeStyles.text,
          { color: config.color },
        ]}
      >
        {config.text}
      </Text>
    </View>
  );
};

// Shared status configuration function
const getStatusConfig = (status: PropertyStatus) => {
  switch (status) {
    case 'active':
      return {
        color: '#10b981',
        backgroundColor: '#f0fdf4',
        text: 'Active',
        icon: 'checkmark-circle' as const,
        description: 'Live and accepting applications',
      };
    case 'rented':
      return {
        color: '#4361ee',
        backgroundColor: '#eff6ff',
        text: 'Rented',
        icon: 'home' as const,
        description: 'Currently occupied',
      };
    case 'maintenance':
      return {
        color: '#f59e0b',
        backgroundColor: '#fffbeb',
        text: 'Maintenance',
        icon: 'construct' as const,
        description: 'Under maintenance',
      };
    case 'draft':
      return {
        color: '#6b7280',
        backgroundColor: '#f9fafb',
        text: 'Draft',
        icon: 'document-text' as const,
        description: 'Not yet published',
      };
    case 'inactive':
      return {
        color: '#ef4444',
        backgroundColor: '#fef2f2',
        text: 'Inactive',
        icon: 'pause-circle' as const,
        description: 'Temporarily disabled',
      };
    default:
      return {
        color: '#6b7280',
        backgroundColor: '#f9fafb',
        text: 'Unknown',
        icon: 'help-circle' as const,
        description: 'Unknown status',
      };
  }
};

// Extended component with description
export const PropertyStatusCard: React.FC<PropertyStatusBadgeProps & { showDescription?: boolean }> = ({
  status,
  size = 'medium',
  showIcon = true,
  showDescription = false,
}) => {
  const config = getStatusConfig(status);

  if (!showDescription) {
    return <PropertyStatusBadge status={status} size={size} showIcon={showIcon} />;
  }

  return (
    <View style={[styles.card, { backgroundColor: config.backgroundColor }]}>
      <View style={styles.cardHeader}>
        {showIcon && (
          <Ionicons
            name={config.icon}
            size={20}
            color={config.color}
            style={styles.cardIcon}
          />
        )}
        <Text style={[styles.cardTitle, { color: config.color }]}>
          {config.text}
        </Text>
      </View>
      <Text style={[styles.cardDescription, { color: config.color }]}>
        {config.description}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  containerSmall: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  containerMedium: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  containerLarge: {
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  icon: {
    marginRight: 4,
  },
  text: {
    fontWeight: '600',
  },
  textSmall: {
    fontSize: 10,
  },
  textMedium: {
    fontSize: 12,
  },
  textLarge: {
    fontSize: 14,
  },
  card: {
    borderRadius: 12,
    padding: 12,
    marginVertical: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  cardIcon: {
    marginRight: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  cardDescription: {
    fontSize: 14,
    opacity: 0.8,
  },
});
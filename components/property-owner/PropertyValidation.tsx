import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { propertyOwnerService } from '../../services/propertyOwnerService';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface PropertyValidationProps {
  propertyId: string;
  onValidationComplete?: (result: ValidationResult) => void;
  showValidation?: boolean;
}

export const PropertyValidation: React.FC<PropertyValidationProps> = ({
  propertyId,
  onValidationComplete,
  showValidation = false,
}) => {
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateProperty = async () => {
    if (!propertyId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await propertyOwnerService.validateProperty(propertyId);
      
      if (response.status === 'success' && response.data) {
        const validationResult = response.data.validation;
        setValidation(validationResult);
        onValidationComplete?.(validationResult);
      } else {
        throw new Error(response.message || 'Validation failed');
      }
    } catch (err: any) {
      console.error('Validation error:', err);
      setError(err.message || 'Failed to validate property');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showValidation && propertyId) {
      validateProperty();
    }
  }, [propertyId, showValidation]);

  if (!showValidation) {
    return null;
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>Validating property...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={20} color="#FF3B30" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={validateProperty} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (!validation) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons 
          name={validation.isValid ? "checkmark-circle" : "alert-circle"} 
          size={20} 
          color={validation.isValid ? "#10b981" : "#f59e0b"} 
        />
        <Text style={styles.headerText}>
          {validation.isValid ? 'Ready to Publish' : 'Needs Attention'}
        </Text>
        <TouchableOpacity onPress={validateProperty} style={styles.refreshButton}>
          <Ionicons name="refresh" size={16} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {validation.errors.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Required Fields Missing:</Text>
          <ScrollView style={styles.listContainer} nestedScrollEnabled>
            {validation.errors.map((error, index) => (
              <View key={index} style={styles.listItem}>
                <Ionicons name="close-circle" size={16} color="#FF3B30" />
                <Text style={styles.errorItemText}>{error}</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      {validation.warnings.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recommendations:</Text>
          <ScrollView style={styles.listContainer} nestedScrollEnabled>
            {validation.warnings.map((warning, index) => (
              <View key={index} style={styles.listItem}>
                <Ionicons name="warning" size={16} color="#f59e0b" />
                <Text style={styles.warningItemText}>{warning}</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      {validation.isValid && validation.errors.length === 0 && (
        <View style={styles.successContainer}>
          <Ionicons name="checkmark-circle" size={24} color="#10b981" />
          <Text style={styles.successText}>
            Your property is ready to be published!
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666666',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  errorText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#FF3B30',
  },
  retryButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: '#F8F9FA',
    borderRadius: 6,
  },
  retryButtonText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  refreshButton: {
    padding: 4,
  },
  section: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  listContainer: {
    maxHeight: 120,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    paddingRight: 8,
  },
  errorItemText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 13,
    color: '#FF3B30',
    lineHeight: 18,
  },
  warningItemText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 13,
    color: '#f59e0b',
    lineHeight: 18,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: '#f0fdf4',
    borderRadius: 8,
    marginTop: 8,
  },
  successText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#10b981',
    fontWeight: '500',
  },
});
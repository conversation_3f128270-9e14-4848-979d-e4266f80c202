import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSelector from './LanguageSelector';

const TranslationExample: React.FC = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('common.loading')}</Text>
      <Text style={styles.subtitle}>{t('navigation.home')}</Text>
      
      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>{t('common.save')}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>{t('auth.login')}</Text>
      </TouchableOpacity>

      <Text style={styles.sectionTitle}>{t('properties.title')}</Text>
      <Text>{t('properties.price')}: €500,000</Text>
      <Text>{t('properties.location')}: Amsterdam</Text>
      <Text>{t('properties.bedrooms')}: 3</Text>

      <LanguageSelector style={styles.languageSelector} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 20,
    color: '#666',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  languageSelector: {
    marginTop: 30,
  },
});

export default TranslationExample;
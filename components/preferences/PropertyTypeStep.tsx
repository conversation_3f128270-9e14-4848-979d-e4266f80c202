import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
} from 'react-native-reanimated';
import { NumberInput } from './NumberInput';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Property types with icons
const PROPERTY_TYPES = [
  {
    id: 'apartment',
    title: 'Apartment',
    description: 'Flat in a multi-unit building',
    icon: 'business-outline',
  },
  {
    id: 'house',
    title: 'House',
    description: 'Standalone residential building',
    icon: 'home-outline',
  },
  {
    id: 'woning',
    title: 'Woning',
    description: 'General dwelling (Dutch term)',
    icon: 'home',
  },
  {
    id: 'studio',
    title: 'Studio',
    description: 'Single room with kitchen and bathroom',
    icon: 'cube-outline',
  },
  {
    id: 'room',
    title: 'Room',
    description: 'Single room in a shared property',
    icon: 'bed-outline',
  },
];

interface PropertyTypeStepProps {
  selectedTypes: string[];
  minRooms: number;
  maxRooms: number;
  onUpdatePropertyPreferences: (types: string[], minRooms: number, maxRooms: number) => void;
}

export const PropertyTypeStep: React.FC<PropertyTypeStepProps> = ({
  selectedTypes,
  minRooms,
  maxRooms,
  onUpdatePropertyPreferences,
}) => {
  // State
  const [localSelectedTypes, setLocalSelectedTypes] = useState<string[]>(selectedTypes);
  const [localMinRooms, setLocalMinRooms] = useState(minRooms);
  const [localMaxRooms, setLocalMaxRooms] = useState(maxRooms);
  
  // Update local state when props change
  useEffect(() => {
    setLocalSelectedTypes(selectedTypes);
    setLocalMinRooms(minRooms);
    setLocalMaxRooms(maxRooms);
  }, [selectedTypes, minRooms, maxRooms]);
  
  // Handle property type selection
  const handleSelectPropertyType = (typeId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    let newSelectedTypes: string[];
    
    if (localSelectedTypes.includes(typeId)) {
      // Remove type if already selected
      newSelectedTypes = localSelectedTypes.filter(id => id !== typeId);
    } else {
      // Add type if not selected
      newSelectedTypes = [...localSelectedTypes, typeId];
    }
    
    // Update local state
    setLocalSelectedTypes(newSelectedTypes);
    
    // Update parent component
    onUpdatePropertyPreferences(newSelectedTypes, localMinRooms, localMaxRooms);
  };
  
  // Handle min rooms change
  const handleMinRoomsChange = (value: number) => {
    const newMinRooms = Math.min(value, localMaxRooms);
    setLocalMinRooms(newMinRooms);
    onUpdatePropertyPreferences(localSelectedTypes, newMinRooms, localMaxRooms);
  };
  
  // Handle max rooms change
  const handleMaxRoomsChange = (value: number) => {
    const newMaxRooms = Math.max(value, localMinRooms);
    setLocalMaxRooms(newMaxRooms);
    onUpdatePropertyPreferences(localSelectedTypes, localMinRooms, newMaxRooms);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>What type of property?</Text>
      <Text style={styles.subtitle}>
        Select the types of properties you're interested in
      </Text>
      
      {/* Property type selection */}
      <View style={styles.propertyTypesContainer}>
        {PROPERTY_TYPES.map((type, index) => (
          <Animated.View
            key={type.id}
            entering={FadeIn.delay(index * 100).duration(400)}
          >
            <TouchableOpacity
              style={[
                styles.propertyTypeCard,
                localSelectedTypes.includes(type.id) && styles.selectedPropertyTypeCard,
              ]}
              onPress={() => handleSelectPropertyType(type.id)}
            >
              <View 
                style={[
                  styles.iconContainer,
                  localSelectedTypes.includes(type.id) && styles.selectedIconContainer,
                ]}
              >
                <Ionicons 
                  name={type.icon as any} 
                  size={24} 
                  color={localSelectedTypes.includes(type.id) ? THEME.light : THEME.accent} 
                />
              </View>
              
              <View style={styles.propertyTypeContent}>
                <Text 
                  style={[
                    styles.propertyTypeTitle,
                    localSelectedTypes.includes(type.id) && styles.selectedPropertyTypeTitle,
                  ]}
                >
                  {type.title}
                </Text>
                <Text 
                  style={[
                    styles.propertyTypeDescription,
                    localSelectedTypes.includes(type.id) && styles.selectedPropertyTypeDescription,
                  ]}
                >
                  {type.description}
                </Text>
              </View>
              
              <View 
                style={[
                  styles.checkContainer,
                  localSelectedTypes.includes(type.id) && styles.selectedCheckContainer,
                ]}
              >
                {localSelectedTypes.includes(type.id) && (
                  <Ionicons name="checkmark" size={20} color={THEME.light} />
                )}
              </View>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
      
      {/* Room range selection */}
      <View style={styles.roomsContainer}>
        <Text style={styles.roomsTitle}>Number of Rooms</Text>
        <Text style={styles.roomsSubtitle}>
          Select the minimum and maximum number of rooms
        </Text>
        
        {/* Room range display */}
        <View style={styles.roomRangeDisplay}>
          <Text style={styles.roomRangeText}>
            {localMinRooms === localMaxRooms 
              ? `${localMinRooms} ${localMinRooms === 1 ? 'Room' : 'Rooms'}`
              : `${localMinRooms} - ${localMaxRooms} Rooms`
            }
          </Text>
        </View>
        
        {/* Room inputs */}
        <View style={styles.roomInputsContainer}>
          <NumberInput
            label="Minimum Rooms"
            value={localMinRooms}
            onValueChange={handleMinRoomsChange}
            minimumValue={1}
            maximumValue={5}
            step={1}
            style={styles.roomInput}
          />
          
          <NumberInput
            label="Maximum Rooms"
            value={localMaxRooms}
            onValueChange={handleMaxRoomsChange}
            minimumValue={1}
            maximumValue={5}
            step={1}
            style={styles.roomInput}
          />
        </View>
      </View>
      
      {/* Info message */}
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          In the Netherlands, room count typically includes living room, bedrooms, and sometimes study rooms
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  propertyTypesContainer: {
    marginBottom: 24,
  },
  propertyTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedPropertyTypeCard: {
    backgroundColor: 'rgba(67, 97, 238, 0.05)',
    borderColor: THEME.primary,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  selectedIconContainer: {
    backgroundColor: THEME.accent,
  },
  propertyTypeContent: {
    flex: 1,
  },
  propertyTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  selectedPropertyTypeTitle: {
    color: THEME.primary,
  },
  propertyTypeDescription: {
    fontSize: 14,
    color: THEME.gray,
  },
  selectedPropertyTypeDescription: {
    color: '#4b5563',
  },
  checkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedCheckContainer: {
    backgroundColor: THEME.primary,
    borderColor: THEME.primary,
  },
  roomsContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  roomsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  roomsSubtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 16,
  },
  roomRangeDisplay: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  roomRangeText: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.primary,
  },
  roomInputsContainer: {
    marginBottom: 16,
  },
  roomInput: {
    marginBottom: 12,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});
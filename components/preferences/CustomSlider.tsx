import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  PanResponder,
  GestureResponderEvent,
  PanResponderGestureState,
  LayoutChangeEvent,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface CustomSliderProps {
  minimumValue: number;
  maximumValue: number;
  value: number;
  step?: number;
  onValueChange: (value: number) => void;
  minimumTrackTintColor?: string;
  maximumTrackTintColor?: string;
  thumbTintColor?: string;
  style?: any;
}

export const CustomSlider: React.FC<CustomSliderProps> = ({
  minimumValue,
  maximumValue,
  value,
  step = 1,
  onValueChange,
  minimumTrackTintColor = THEME.accent,
  maximumTrackTintColor = '#e5e7eb',
  thumbTintColor = THEME.accent,
  style,
}) => {
  // State
  const [sliderWidth, setSliderWidth] = useState(0);
  const [localValue, setLocalValue] = useState(value);
  const [lastHapticValue, setLastHapticValue] = useState(value);
  const [isDragging, setIsDragging] = useState(false);
  
  // Animation value
  const thumbPosition = useSharedValue(0);
  
  // Update local value when prop changes (but not during dragging)
  useEffect(() => {
    if (!isDragging) {
      setLocalValue(value);
      updateThumbPosition(value);
    }
  }, [value, isDragging]);
  
  // Update thumb position based on value
  const updateThumbPosition = (val: number, immediate = false) => {
    if (sliderWidth === 0) return;
    
    const percentage = (val - minimumValue) / (maximumValue - minimumValue);
    // Account for thumb width to center it properly
    const thumbWidth = 24;
    const availableWidth = sliderWidth - thumbWidth;
    const position = percentage * availableWidth;
    
    if (immediate || isDragging) {
      thumbPosition.value = position;
    } else {
      thumbPosition.value = withTiming(position, {
        duration: 100,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
    }
  };
  
  // Handle layout change
  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setSliderWidth(width);
    updateThumbPosition(localValue);
  };
  
  // Calculate value from position
  const getValueFromPosition = (position: number) => {
    if (sliderWidth === 0) return minimumValue;
    
    // Account for thumb width to get accurate positioning
    const thumbWidth = 24;
    const availableWidth = sliderWidth - thumbWidth;
    const adjustedPosition = Math.max(0, Math.min(availableWidth, position - thumbWidth / 2));
    const percentage = adjustedPosition / availableWidth;
    
    let rawValue = minimumValue + percentage * (maximumValue - minimumValue);
    
    // Apply step if provided
    if (step > 0) {
      rawValue = Math.round(rawValue / step) * step;
    }
    
    return Math.max(minimumValue, Math.min(maximumValue, rawValue));
  };
  
  // Track initial touch position
  const [initialTouchX, setInitialTouchX] = useState(0);
  const [initialValue, setInitialValue] = useState(0);

  // Create pan responder
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: (event: GestureResponderEvent) => {
      setIsDragging(true);
      const position = event.nativeEvent.locationX;
      setInitialTouchX(position);
      setInitialValue(localValue);
      
      // Set value based on tap position
      const newValue = getValueFromPosition(position);
      setLocalValue(newValue);
      onValueChange(newValue);
      updateThumbPosition(newValue, true);
      
      // Provide haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    },
    onPanResponderMove: (event: GestureResponderEvent, gestureState: PanResponderGestureState) => {
      // Calculate position based on initial touch and gesture movement
      const currentPosition = initialTouchX + gestureState.dx;
      const newValue = getValueFromPosition(currentPosition);
      
      // Provide haptic feedback on significant value changes
      if (Math.abs(newValue - lastHapticValue) >= step * 2) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setLastHapticValue(newValue);
      }
      
      setLocalValue(newValue);
      onValueChange(newValue);
      updateThumbPosition(newValue, true);
    },
    onPanResponderRelease: () => {
      // Reset tracking values
      setInitialTouchX(0);
      setInitialValue(0);
      setIsDragging(false);
    },
  });
  
  // Calculate percentage for track width
  const percentage = (localValue - minimumValue) / (maximumValue - minimumValue);
  const trackWidth = percentage * 100;
  
  // Animated styles
  const thumbStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: thumbPosition.value }],
    };
  });
  
  return (
    <View style={[styles.container, style]} onLayout={handleLayout}>
      <View 
        style={styles.track} 
        {...panResponder.panHandlers}
      >
        <View 
          style={[
            styles.minimumTrack, 
            { 
              width: `${trackWidth}%`,
              backgroundColor: minimumTrackTintColor,
            }
          ]} 
        />
        <View 
          style={[
            styles.maximumTrack, 
            { 
              width: `${100 - trackWidth}%`,
              backgroundColor: maximumTrackTintColor,
            }
          ]} 
        />
        <Animated.View 
          style={[
            styles.thumb, 
            { backgroundColor: thumbTintColor },
            thumbStyle,
          ]} 
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 44,
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  track: {
    height: 6,
    borderRadius: 3,
    flexDirection: 'row',
    overflow: 'visible',
    backgroundColor: '#e5e7eb',
  },
  minimumTrack: {
    height: '100%',
    borderTopLeftRadius: 3,
    borderBottomLeftRadius: 3,
  },
  maximumTrack: {
    height: '100%',
    borderTopRightRadius: 3,
    borderBottomRightRadius: 3,
  },
  thumb: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    top: -10,
    left: -12,
    borderWidth: 2,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
});
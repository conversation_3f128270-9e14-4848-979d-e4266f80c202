import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Notification types
const NOTIFICATION_TYPES = [
  {
    id: 'email',
    title: 'Email Notifications',
    description: 'Receive updates and alerts via email',
    icon: 'mail-outline',
  },
  {
    id: 'push',
    title: 'Push Notifications',
    description: 'Get instant alerts on your device',
    icon: 'notifications-outline',
  },
  {
    id: 'sms',
    title: 'SMS Notifications',
    description: 'Receive text messages for urgent updates',
    icon: 'chatbubble-outline',
  },
];

// Notification categories
const NOTIFICATION_CATEGORIES = [
  {
    id: 'newMatches',
    title: 'New Property Matches',
    description: 'When new properties match your preferences',
    defaultEnabled: true,
  },
  {
    id: 'priceChanges',
    title: 'Price Changes',
    description: 'When prices change on saved properties',
    defaultEnabled: true,
  },
  {
    id: 'applicationUpdates',
    title: 'Application Updates',
    description: 'Status updates on your rental applications',
    defaultEnabled: true,
  },
  {
    id: 'marketInsights',
    title: 'Market Insights',
    description: 'Trends and insights about your preferred areas',
    defaultEnabled: false,
  },
];

interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
}

interface NotificationStepProps {
  notificationSettings: NotificationSettings;
  onUpdateNotifications: (settings: NotificationSettings) => void;
}

export const NotificationStep: React.FC<NotificationStepProps> = ({
  notificationSettings,
  onUpdateNotifications,
}) => {
  // State
  const [localSettings, setLocalSettings] = useState<NotificationSettings>(notificationSettings);
  const [categorySettings, setCategorySettings] = useState<Record<string, boolean>>({
    newMatches: true,
    priceChanges: true,
    applicationUpdates: true,
    marketInsights: false,
  });
  
  // Update local state when props change
  useEffect(() => {
    setLocalSettings(notificationSettings);
  }, [notificationSettings]);
  
  // Handle notification type toggle
  const handleToggleNotificationType = (typeId: keyof NotificationSettings) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    const newSettings = {
      ...localSettings,
      [typeId]: !localSettings[typeId],
    };
    
    // Update local state
    setLocalSettings(newSettings);
    
    // Update parent component
    onUpdateNotifications(newSettings);
  };
  
  // Handle notification category toggle
  const handleToggleCategory = (categoryId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    const newCategorySettings = {
      ...categorySettings,
      [categoryId]: !categorySettings[categoryId],
    };
    
    // Update local state
    setCategorySettings(newCategorySettings);
  };
  
  // Check if any notification type is enabled
  const isAnyNotificationEnabled = () => {
    return Object.values(localSettings).some(value => value);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notification Preferences</Text>
      <Text style={styles.subtitle}>
        Choose how you'd like to be notified about properties and updates
      </Text>
      
      {/* Notification types */}
      <View style={styles.notificationTypesContainer}>
        {NOTIFICATION_TYPES.map((type, index) => (
          <Animated.View
            key={type.id}
            entering={FadeIn.delay(index * 100).duration(400)}
          >
            <View style={styles.notificationTypeCard}>
              <View style={styles.notificationTypeContent}>
                <View style={styles.notificationTypeHeader}>
                  <View 
                    style={[
                      styles.iconContainer,
                      localSettings[type.id as keyof NotificationSettings] && styles.activeIconContainer,
                    ]}
                  >
                    <Ionicons 
                      name={type.icon as any} 
                      size={20} 
                      color={localSettings[type.id as keyof NotificationSettings] ? THEME.light : THEME.accent} 
                    />
                  </View>
                  <View style={styles.notificationTypeTitleContainer}>
                    <Text style={styles.notificationTypeTitle}>{type.title}</Text>
                    <Text style={styles.notificationTypeDescription}>{type.description}</Text>
                  </View>
                </View>
              </View>
              
              <Switch
                value={localSettings[type.id as keyof NotificationSettings]}
                onValueChange={() => handleToggleNotificationType(type.id as keyof NotificationSettings)}
                trackColor={{ false: '#e5e7eb', true: 'rgba(67, 97, 238, 0.4)' }}
                thumbColor={localSettings[type.id as keyof NotificationSettings] ? THEME.primary : '#f4f3f4'}
                ios_backgroundColor="#e5e7eb"
              />
            </View>
          </Animated.View>
        ))}
      </View>
      
      {/* Notification categories */}
      {isAnyNotificationEnabled() && (
        <Animated.View 
          style={styles.categoriesContainer}
          entering={FadeIn.duration(400)}
        >
          <Text style={styles.sectionTitle}>What would you like to be notified about?</Text>
          
          {NOTIFICATION_CATEGORIES.map((category, index) => (
            <View key={category.id} style={styles.categoryCard}>
              <View style={styles.categoryContent}>
                <Text style={styles.categoryTitle}>{category.title}</Text>
                <Text style={styles.categoryDescription}>{category.description}</Text>
              </View>
              
              <Switch
                value={categorySettings[category.id]}
                onValueChange={() => handleToggleCategory(category.id)}
                trackColor={{ false: '#e5e7eb', true: 'rgba(67, 97, 238, 0.4)' }}
                thumbColor={categorySettings[category.id] ? THEME.primary : '#f4f3f4'}
                ios_backgroundColor="#e5e7eb"
              />
            </View>
          ))}
        </Animated.View>
      )}
      
      {/* Info message */}
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          You can change your notification preferences at any time in your account settings
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  notificationTypesContainer: {
    marginBottom: 24,
  },
  notificationTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  notificationTypeContent: {
    flex: 1,
    marginRight: 16,
  },
  notificationTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activeIconContainer: {
    backgroundColor: THEME.accent,
  },
  notificationTypeTitleContainer: {
    flex: 1,
  },
  notificationTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  notificationTypeDescription: {
    fontSize: 14,
    color: THEME.gray,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  categoriesContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  categoryContent: {
    flex: 1,
    marginRight: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: THEME.gray,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});
import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
} from 'react-native-reanimated';
import { NumberInput } from './NumberInput';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Budget presets based on user profile
const BUDGET_PRESETS: Record<string, { min: number; max: number }> = {
  student: { min: 500, max: 1200 },
  expat: { min: 1000, max: 2000 },
  professional: { min: 1200, max: 2500 },
  default: { min: 800, max: 1800 },
};

interface BudgetStepProps {
  minPrice: number;
  maxPrice: number;
  onUpdateBudget: (min: number, max: number) => void;
  marketData?: any;
  userProfile?: 'student' | 'expat' | 'professional' | null;
  selectedLocations?: string[];
}

export const BudgetStep: React.FC<BudgetStepProps> = ({
  minPrice,
  maxPrice,
  onUpdateBudget,
  marketData,
  userProfile,
  selectedLocations = [],
}) => {
  // State
  const [localMinPrice, setLocalMinPrice] = useState(minPrice);
  const [localMaxPrice, setLocalMaxPrice] = useState(maxPrice);
  const [averagePrice, setAveragePrice] = useState<number | null>(null);
  const [priceRange, setPriceRange] = useState<{ min: number; max: number } | null>(null);
  
  // Extract market data
  useEffect(() => {
    if (marketData?.analysis) {
      setAveragePrice(marketData.analysis.averagePrice);
      setPriceRange(marketData.analysis.priceRange);
    }
  }, [marketData]);
  
  // Update local state when props change
  useEffect(() => {
    setLocalMinPrice(minPrice);
    setLocalMaxPrice(maxPrice);
  }, [minPrice, maxPrice]);
  
  // Apply budget preset
  const applyBudgetPreset = (preset: { min: number; max: number }) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setLocalMinPrice(preset.min);
    setLocalMaxPrice(preset.max);
    onUpdateBudget(preset.min, preset.max);
  };
  
  // Handle min price change
  const handleMinPriceChange = (value: number) => {
    const newMinPrice = Math.min(value, localMaxPrice - 100);
    setLocalMinPrice(newMinPrice);
    onUpdateBudget(newMinPrice, localMaxPrice);
  };
  
  // Handle max price change
  const handleMaxPriceChange = (value: number) => {
    const newMaxPrice = Math.max(value, localMinPrice + 100);
    setLocalMaxPrice(newMaxPrice);
    onUpdateBudget(localMinPrice, newMaxPrice);
  };
  
  // Format price for display
  const formatPrice = (price: number) => {
    return `€${price.toLocaleString()}`;
  };
  
  // Get budget recommendation based on market data and user profile
  const getBudgetRecommendation = () => {
    if (averagePrice) {
      // Get preset based on user profile or default
      const preset = userProfile ? BUDGET_PRESETS[userProfile] : BUDGET_PRESETS.default;
      
      // Adjust based on market data
      return {
        min: Math.round(Math.min(preset.min, averagePrice * 0.7)),
        max: Math.round(Math.max(preset.max, averagePrice * 1.2)),
      };
    }
    
    // Fallback to preset if no market data
    return userProfile ? BUDGET_PRESETS[userProfile] : BUDGET_PRESETS.default;
  };
  
  // Get budget recommendation
  const recommendation = getBudgetRecommendation();
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>What's your rental budget?</Text>
      <Text style={styles.subtitle}>
        Set your minimum and maximum monthly rent
      </Text>
      
      {/* Budget range display */}
      <View style={styles.budgetDisplayContainer}>
        <View style={styles.budgetDisplay}>
          <Text style={styles.budgetLabel}>Minimum</Text>
          <Text style={styles.budgetValue}>{formatPrice(localMinPrice)}</Text>
        </View>
        
        <View style={styles.budgetSeparator}>
          <Ionicons name="remove" size={24} color={THEME.gray} />
        </View>
        
        <View style={styles.budgetDisplay}>
          <Text style={styles.budgetLabel}>Maximum</Text>
          <Text style={styles.budgetValue}>{formatPrice(localMaxPrice)}</Text>
        </View>
      </View>
      
      {/* Price inputs */}
      <View style={styles.inputsContainer}>
        <NumberInput
          label="Minimum Price"
          value={localMinPrice}
          onValueChange={handleMinPriceChange}
          minimumValue={300}
          maximumValue={3000}
          step={50}
          prefix="€"
          style={styles.priceInput}
        />
        
        <NumberInput
          label="Maximum Price"
          value={localMaxPrice}
          onValueChange={handleMaxPriceChange}
          minimumValue={500}
          maximumValue={5000}
          step={50}
          prefix="€"
          style={styles.priceInput}
        />
      </View>
      
      {/* Budget presets */}
      <View style={styles.presetsContainer}>
        <Text style={styles.presetsTitle}>Quick Presets</Text>
        <View style={styles.presetButtons}>
          <TouchableOpacity
            style={styles.presetButton}
            onPress={() => applyBudgetPreset(BUDGET_PRESETS.student)}
          >
            <Text style={styles.presetButtonText}>Student</Text>
            <Text style={styles.presetButtonRange}>€500 - €1,200</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.presetButton}
            onPress={() => applyBudgetPreset(BUDGET_PRESETS.expat)}
          >
            <Text style={styles.presetButtonText}>Expat</Text>
            <Text style={styles.presetButtonRange}>€1,000 - €2,000</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.presetButton}
            onPress={() => applyBudgetPreset(BUDGET_PRESETS.professional)}
          >
            <Text style={styles.presetButtonText}>Professional</Text>
            <Text style={styles.presetButtonRange}>€1,200 - €2,500</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Market insights */}
      {averagePrice && (
        <Animated.View 
          style={styles.marketInsightsContainer}
          entering={FadeIn.duration(400)}
        >
          <View style={styles.marketInsightHeader}>
            <Ionicons name="analytics-outline" size={20} color={THEME.primary} />
            <Text style={styles.marketInsightTitle}>
              Market Insights {selectedLocations.length > 0 ? `for ${selectedLocations[0]}` : ''}
            </Text>
          </View>
          
          <View style={styles.marketInsightContent}>
            <View style={styles.marketInsightItem}>
              <Text style={styles.marketInsightLabel}>Average Price</Text>
              <Text style={styles.marketInsightValue}>{formatPrice(averagePrice)}</Text>
            </View>
            
            {priceRange && (
              <View style={styles.marketInsightItem}>
                <Text style={styles.marketInsightLabel}>Market Range</Text>
                <Text style={styles.marketInsightValue}>
                  {formatPrice(priceRange.min)} - {formatPrice(priceRange.max)}
                </Text>
              </View>
            )}
            
            <View style={styles.marketInsightItem}>
              <Text style={styles.marketInsightLabel}>Recommended</Text>
              <Text style={styles.marketInsightValue}>
                {formatPrice(recommendation.min)} - {formatPrice(recommendation.max)}
              </Text>
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.applyRecommendationButton}
            onPress={() => applyBudgetPreset(recommendation)}
          >
            <Text style={styles.applyRecommendationText}>Apply Recommendation</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
      
      {/* Info message */}
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          Setting a realistic budget increases your chances of finding suitable properties
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  budgetDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    padding: 16,
    marginBottom: 24,
  },
  budgetDisplay: {
    alignItems: 'center',
  },
  budgetLabel: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 4,
  },
  budgetValue: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
  },
  budgetSeparator: {
    paddingHorizontal: 16,
  },
  inputsContainer: {
    marginBottom: 24,
  },
  priceInput: {
    marginBottom: 16,
  },
  presetsContainer: {
    marginBottom: 24,
  },
  presetsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 12,
  },
  presetButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  presetButton: {
    flex: 1,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  presetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  presetButtonRange: {
    fontSize: 12,
    color: THEME.gray,
  },
  marketInsightsContainer: {
    backgroundColor: 'rgba(67, 97, 238, 0.05)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(67, 97, 238, 0.2)',
    padding: 16,
    marginBottom: 24,
  },
  marketInsightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  marketInsightTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.primary,
    marginLeft: 8,
  },
  marketInsightContent: {
    marginBottom: 16,
  },
  marketInsightItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  marketInsightLabel: {
    fontSize: 14,
    color: '#4b5563',
  },
  marketInsightValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  applyRecommendationButton: {
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  applyRecommendationText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.primary,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});
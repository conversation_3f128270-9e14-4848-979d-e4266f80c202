import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
};

interface NumberInputProps {
  value: number;
  onValueChange: (value: number) => void;
  minimumValue?: number;
  maximumValue?: number;
  step?: number;
  label?: string;
  prefix?: string;
  suffix?: string;
  style?: any;
}

export const NumberInput: React.FC<NumberInputProps> = ({
  value,
  onValueChange,
  minimumValue = 0,
  maximumValue = 10000,
  step = 1,
  label,
  prefix = '',
  suffix = '',
  style,
}) => {
  const [localValue, setLocalValue] = useState(value.toString());
  const [isFocused, setIsFocused] = useState(false);

  // Update local value when prop changes
  useEffect(() => {
    setLocalValue(value.toString());
  }, [value]);

  // Handle text input change
  const handleTextChange = (text: string) => {
    // Remove non-numeric characters except for decimal point
    const cleanText = text.replace(/[^0-9]/g, '');
    setLocalValue(cleanText);

    // Convert to number and validate
    const numValue = parseInt(cleanText) || 0;
    const clampedValue = Math.max(minimumValue, Math.min(maximumValue, numValue));
    
    if (numValue !== clampedValue) {
      setLocalValue(clampedValue.toString());
    }
    
    onValueChange(clampedValue);
  };

  // Handle text input blur
  const handleBlur = () => {
    setIsFocused(false);
    const numValue = parseInt(localValue) || minimumValue;
    const clampedValue = Math.max(minimumValue, Math.min(maximumValue, numValue));
    setLocalValue(clampedValue.toString());
    onValueChange(clampedValue);
  };

  // Handle increment
  const handleIncrement = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const currentValue = parseInt(localValue) || 0;
    const newValue = Math.min(maximumValue, currentValue + step);
    setLocalValue(newValue.toString());
    onValueChange(newValue);
  };

  // Handle decrement
  const handleDecrement = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const currentValue = parseInt(localValue) || 0;
    const newValue = Math.max(minimumValue, currentValue - step);
    setLocalValue(newValue.toString());
    onValueChange(newValue);
  };

  const currentValue = parseInt(localValue) || 0;
  const canDecrement = currentValue > minimumValue;
  const canIncrement = currentValue < maximumValue;

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={[styles.inputContainer, isFocused && styles.inputContainerFocused]}>
        <TouchableOpacity
          style={[styles.button, !canDecrement && styles.buttonDisabled]}
          onPress={handleDecrement}
          disabled={!canDecrement}
          activeOpacity={0.7}
        >
          <Ionicons 
            name="remove" 
            size={20} 
            color={canDecrement ? THEME.primary : THEME.gray} 
          />
        </TouchableOpacity>

        <View style={styles.valueContainer}>
          <Text style={styles.prefix}>{prefix}</Text>
          <TextInput
            style={styles.textInput}
            value={localValue}
            onChangeText={handleTextChange}
            onFocus={() => setIsFocused(true)}
            onBlur={handleBlur}
            keyboardType="numeric"
            selectTextOnFocus
            textAlign="center"
          />
          <Text style={styles.suffix}>{suffix}</Text>
        </View>

        <TouchableOpacity
          style={[styles.button, !canIncrement && styles.buttonDisabled]}
          onPress={handleIncrement}
          disabled={!canIncrement}
          activeOpacity={0.7}
        >
          <Ionicons 
            name="add" 
            size={20} 
            color={canIncrement ? THEME.primary : THEME.gray} 
          />
        </TouchableOpacity>
      </View>

      <View style={styles.rangeInfo}>
        <Text style={styles.rangeText}>
          Range: {prefix}{minimumValue.toLocaleString()}{suffix} - {prefix}{maximumValue.toLocaleString()}{suffix}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.light,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  inputContainerFocused: {
    borderColor: THEME.primary,
    shadowColor: THEME.primary,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  button: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: THEME.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  buttonDisabled: {
    backgroundColor: '#f9fafb',
    opacity: 0.5,
  },
  valueContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  prefix: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.primary,
    marginRight: 4,
  },
  textInput: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
    minWidth: 60,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  suffix: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.primary,
    marginLeft: 4,
  },
  rangeInfo: {
    marginTop: 4,
    alignItems: 'center',
  },
  rangeText: {
    fontSize: 12,
    color: THEME.gray,
  },
});
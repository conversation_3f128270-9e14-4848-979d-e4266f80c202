# Smart Preferences Wizard

This directory contains components for the Smart Preferences Setup Wizard, which provides an intelligent, multi-step interface for users to configure their rental preferences.

## Dependencies

The Smart Preferences Wizard requires the following dependencies:

- `@ptomasroos/react-native-multi-slider`: For the range slider components used in the Budget and Property Type steps.

To install this dependency, run:

```bash
npm install @ptomasroos/react-native-multi-slider
```

## Components

- `SmartPreferencesWizard`: The main wizard component that manages the multi-step flow
- `ProfileTypeStep`: For selecting user profile type (student, expat, professional)
- `LocationStep`: For selecting preferred locations with autocomplete
- `BudgetStep`: For setting budget range with market insights
- `PropertyTypeStep`: For selecting property types and room preferences
- `AmenitiesStep`: For selecting preferred amenities
- `NotificationStep`: For configuring notification preferences
- `SummaryStep`: For reviewing all preferences before saving

## Usage

```jsx
import { SmartPreferencesWizard } from '../components/SmartPreferencesWizard';
import { UserPreferences } from '../services/authService';

// In your component
const handleSavePreferences = (preferences: UserPreferences) => {
  // Save preferences and navigate
};

return (
  <SmartPreferencesWizard
    onComplete={handleSavePreferences}
    initialPreferences={user?.preferences}
  />
);
```

## Placeholder Images

The wizard uses placeholder images for development. In a production environment, replace the placeholders with actual images in the `assets/images` directory:

- `profile-student.png`
- `profile-expat.png`
- `profile-professional.png`
- `property-apartment.png`
- `property-house.png`
- `property-room.png`
- `property-studio.png`
import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { listingsService } from '../../services/listingsService';
import { aiService } from '../../services/aiService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Popular cities in the Netherlands
const POPULAR_CITIES = [
  'Amsterdam',
  'Rotterdam',
  'Utrecht',
  'The Hague',
  'Eindhoven',
  'Groningen',
  'Tilburg',
  'Almere',
  'Breda',
  'Nijmegen',
];

interface LocationStepProps {
  selectedLocations: string[];
  onSelectLocations: (locations: string[]) => void;
  isLoading?: boolean;
  onLocationMarketData?: (location: string, data: any) => void;
}

export const LocationStep: React.FC<LocationStepProps> = ({
  selectedLocations,
  onSelectLocations,
  isLoading = false,
  onLocationMarketData,
}) => {
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [loadingLocation, setLoadingLocation] = useState<string | null>(null);
  
  // Load available cities on mount
  useEffect(() => {
    loadAvailableCities();
  }, []);
  
  // Load market data for selected location
  useEffect(() => {
    if (selectedLocations.length > 0 && onLocationMarketData) {
      fetchMarketData(selectedLocations[0]);
    }
  }, [selectedLocations]);
  
  // Load available cities from API
  const loadAvailableCities = async () => {
    try {
      const response = await listingsService.getAvailableCities();
      if (response.success && response.data) {
        setAvailableCities(response.data);
      }
    } catch (error) {
      console.error('Failed to load available cities:', error);
      // Fallback to popular cities
      setAvailableCities(POPULAR_CITIES);
    }
  };
  
  // Fetch market data for a location
  const fetchMarketData = async (location: string) => {
    if (!onLocationMarketData) return;
    
    try {
      setLoadingLocation(location);
      const response = await aiService.getMarketAnalysis({
        location,
        timeframe: '3months',
      });
      
      if (response.success && response.data) {
        onLocationMarketData(location, {
          location,
          analysis: response.data,
          lastUpdated: new Date(),
          userSpecific: false,
        });
      }
    } catch (error) {
      console.error('Failed to fetch market data:', error);
    } finally {
      setLoadingLocation(null);
    }
  };
  
  // Handle search query change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    
    if (text.length > 1) {
      setIsSearching(true);
      
      // Filter available cities based on search query
      const results = availableCities.filter(city => 
        city.toLowerCase().includes(text.toLowerCase())
      );
      
      setSearchResults(results);
      setIsSearching(false);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  };
  
  // Handle location selection
  const handleSelectLocation = (location: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Check if location is already selected
    if (selectedLocations.includes(location)) {
      // Remove location
      onSelectLocations(selectedLocations.filter(loc => loc !== location));
    } else {
      // Add location (max 3)
      if (selectedLocations.length < 3) {
        onSelectLocations([...selectedLocations, location]);
      } else {
        // Replace the last location
        const newLocations = [...selectedLocations];
        newLocations[2] = location;
        onSelectLocations(newLocations);
        
        // Provide haptic feedback to indicate replacement
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    }
    
    // Clear search
    setSearchQuery('');
    setSearchResults([]);
  };
  
  // Render location chip
  const renderLocationChip = (location: string, isSelected: boolean = false) => (
    <TouchableOpacity
      key={location}
      style={[
        styles.locationChip,
        isSelected && styles.selectedLocationChip,
      ]}
      onPress={() => handleSelectLocation(location)}
    >
      <Text 
        style={[
          styles.locationChipText,
          isSelected && styles.selectedLocationChipText,
        ]}
      >
        {location}
      </Text>
      
      {isSelected && (
        <Ionicons name="close-circle" size={16} color={THEME.light} style={styles.chipIcon} />
      )}
      
      {loadingLocation === location && (
        <ActivityIndicator size="small" color={isSelected ? THEME.light : THEME.accent} style={styles.chipLoader} />
      )}
    </TouchableOpacity>
  );
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Where would you like to live?</Text>
      <Text style={styles.subtitle}>
        Select up to 3 cities or areas you're interested in
      </Text>
      
      {/* Search input */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={THEME.gray} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search cities..."
          value={searchQuery}
          onChangeText={handleSearchChange}
          placeholderTextColor="#9ca3af"
        />
        {isSearching && (
          <ActivityIndicator size="small" color={THEME.accent} style={styles.searchLoader} />
        )}
      </View>
      
      {/* Selected locations */}
      {selectedLocations.length > 0 && (
        <View style={styles.selectedLocationsContainer}>
          <Text style={styles.sectionTitle}>Selected Locations</Text>
          <View style={styles.chipsContainer}>
            {selectedLocations.map(location => renderLocationChip(location, true))}
          </View>
        </View>
      )}
      
      {/* Search results */}
      {searchResults.length > 0 && (
        <View style={styles.searchResultsContainer}>
          <Text style={styles.sectionTitle}>Search Results</Text>
          <View style={styles.chipsContainer}>
            {searchResults.map(location => 
              !selectedLocations.includes(location) && renderLocationChip(location)
            )}
          </View>
        </View>
      )}
      
      {/* Popular cities */}
      {searchQuery.length === 0 && (
        <Animated.View 
          style={styles.popularCitiesContainer}
          entering={FadeIn.duration(300)}
        >
          <Text style={styles.sectionTitle}>Popular Cities</Text>
          <View style={styles.chipsContainer}>
            {POPULAR_CITIES.map(city => 
              !selectedLocations.includes(city) && renderLocationChip(city)
            )}
          </View>
        </Animated.View>
      )}
      
      {/* Info message */}
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          Our AI will find properties in and around your selected locations
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1f2937',
  },
  searchLoader: {
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 12,
  },
  selectedLocationsContainer: {
    marginBottom: 24,
  },
  searchResultsContainer: {
    marginBottom: 24,
  },
  popularCitiesContainer: {
    marginBottom: 24,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  locationChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedLocationChip: {
    backgroundColor: THEME.accent,
  },
  locationChipText: {
    fontSize: 14,
    color: THEME.accent,
    fontWeight: '500',
  },
  selectedLocationChipText: {
    color: THEME.light,
  },
  chipIcon: {
    marginLeft: 8,
  },
  chipLoader: {
    marginLeft: 8,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});
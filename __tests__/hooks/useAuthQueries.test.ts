import { renderHook, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useCurrentUser, useLogin, useLogout, useUpdatePreferences } from '../../hooks/useAuthQueries';
import { authService } from '../../services/authService';

// Mock the auth service
jest.mock('../../services/authService');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

// Test wrapper
const createWrapper = () => {
  const testQueryClient = createTestQueryClient();
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={testQueryClient}>
      {children}
    </QueryClientProvider>
  );
};

const mockUser = {
  _id: 'user123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  preferences: {
    minPrice: 800,
    maxPrice: 1500,
    minRooms: 2,
    propertyTypes: ['apartment'],
    preferredCities: ['Amsterdam'],
    matchThreshold: 70,
    prioritizeNewListings: true,
    includeSlightlyOverBudget: false,
    alertFrequency: 'daily',
    quietHours: { start: '22:00', end: '08:00' },
  },
};

describe('Auth Queries', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useCurrentUser', () => {
    it('should fetch current user successfully', async () => {
      mockAuthService.getCurrentUser.mockResolvedValue({
        success: true,
        data: mockUser,
      });

      const { result } = renderHook(() => useCurrentUser(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUser);
      expect(mockAuthService.getCurrentUser).toHaveBeenCalledTimes(1);
    });

    it('should handle fetch user error', async () => {
      mockAuthService.getCurrentUser.mockResolvedValue({
        success: false,
        message: 'User not found',
      });

      const { result } = renderHook(() => useCurrentUser(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeTruthy();
    });
  });

  describe('useLogin', () => {
    it('should login successfully', async () => {
      mockAuthService.login.mockResolvedValue({
        success: true,
        data: { user: mockUser, token: 'test-token' },
      });

      const { result } = renderHook(() => useLogin(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        email: '<EMAIL>',
        password: 'password123',
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({ user: mockUser, token: 'test-token' });
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    it('should handle login error', async () => {
      mockAuthService.login.mockResolvedValue({
        success: false,
        message: 'Invalid credentials',
      });

      const { result } = renderHook(() => useLogin(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Invalid credentials');
    });
  });

  describe('useLogout', () => {
    it('should logout successfully', async () => {
      mockAuthService.logout.mockResolvedValue(undefined);

      const { result } = renderHook(() => useLogout(), {
        wrapper: createWrapper(),
      });

      result.current.mutate();

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockAuthService.logout).toHaveBeenCalledTimes(1);
    });
  });

  describe('useUpdatePreferences', () => {
    it('should update preferences successfully', async () => {
      const updatedUser = {
        ...mockUser,
        preferences: {
          ...mockUser.preferences,
          maxPrice: 2000,
        },
      };

      mockAuthService.updatePreferences.mockResolvedValue({
        success: true,
        data: updatedUser,
      });

      const { result } = renderHook(() => useUpdatePreferences(), {
        wrapper: createWrapper(),
      });

      const newPreferences = { maxPrice: 2000 };
      result.current.mutate(newPreferences);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(updatedUser);
      expect(mockAuthService.updatePreferences).toHaveBeenCalledWith(newPreferences);
    });

    it('should handle update preferences error', async () => {
      mockAuthService.updatePreferences.mockResolvedValue({
        success: false,
        message: 'Update failed',
      });

      const { result } = renderHook(() => useUpdatePreferences(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({ maxPrice: 2000 });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Update failed');
    });
  });
});
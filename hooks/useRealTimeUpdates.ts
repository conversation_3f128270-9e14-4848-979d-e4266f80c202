import { useState, useEffect, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { useAIStore } from '../store/aiStore';
import { realTimeUpdateService, RefreshStrategy } from '../services/realTimeUpdateService';
import { registerBackgroundTasks, isBackgroundFetchAvailable } from '../services/backgroundTaskService';

export const useRealTimeUpdates = (autoStart: boolean = true) => {
  const [isActive, setIsActive] = useState(false);
  const [refreshStrategy, setRefreshStrategy] = useState<RefreshStrategy>(
    realTimeUpdateService.getRefreshStrategy()
  );
  const [backgroundTasksAvailable, setBackgroundTasksAvailable] = useState<boolean | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(refreshStrategy.lastRefresh);
  
  const { user } = useAuthStore();
  const { matches, lastMatchUpdate } = useAIStore();
  
  // Start real-time updates
  const startUpdates = useCallback((interval?: number) => {
    realTimeUpdateService.startRealTimeUpdates(interval);
    setIsActive(true);
    setRefreshStrategy(realTimeUpdateService.getRefreshStrategy());
  }, []);
  
  // Stop real-time updates
  const stopUpdates = useCallback(() => {
    realTimeUpdateService.stopRealTimeUpdates();
    setIsActive(false);
    setRefreshStrategy(realTimeUpdateService.getRefreshStrategy());
  }, []);
  
  // Refresh matches immediately
  const refreshNow = useCallback(async () => {
    const result = await realTimeUpdateService.refreshMatches();
    setRefreshStrategy(realTimeUpdateService.getRefreshStrategy());
    setLastRefreshTime(new Date());
    return result;
  }, []);
  
  // Update refresh strategy
  const updateRefreshStrategy = useCallback((strategy: Partial<RefreshStrategy>) => {
    realTimeUpdateService.updateRefreshStrategy(strategy);
    setRefreshStrategy(realTimeUpdateService.getRefreshStrategy());
  }, []);
  
  // Learn from user interactions
  const learnFromInteractions = useCallback(async () => {
    return await realTimeUpdateService.learnFromUserInteractions();
  }, []);
  
  // Register background tasks
  const setupBackgroundTasks = useCallback(async () => {
    const available = await isBackgroundFetchAvailable();
    setBackgroundTasksAvailable(available);
    
    if (available) {
      await registerBackgroundTasks();
    }
    
    return available;
  }, []);
  
  // Handle app state changes
  const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      // App came to foreground, refresh matches if needed
      const lastUpdate = lastMatchUpdate ? new Date(lastMatchUpdate) : null;
      const now = new Date();
      
      // Refresh if last update was more than 15 minutes ago
      if (!lastUpdate || (now.getTime() - lastUpdate.getTime() > 15 * 60 * 1000)) {
        refreshNow();
      }
    }
  }, [lastMatchUpdate, refreshNow]);
  
  // Start updates when component mounts
  useEffect(() => {
    if (autoStart && user) {
      startUpdates();
    }
    
    // Check if background tasks are available
    setupBackgroundTasks();
    
    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      // Clean up
      stopUpdates();
      subscription.remove();
    };
  }, [autoStart, user, startUpdates, stopUpdates, setupBackgroundTasks, handleAppStateChange]);
  
  // Update last refresh time when lastMatchUpdate changes
  useEffect(() => {
    if (lastMatchUpdate) {
      setLastRefreshTime(new Date(lastMatchUpdate));
    }
  }, [lastMatchUpdate]);
  
  return {
    isActive,
    refreshStrategy,
    lastRefreshTime,
    backgroundTasksAvailable,
    startUpdates,
    stopUpdates,
    refreshNow,
    updateRefreshStrategy,
    learnFromInteractions,
    setupBackgroundTasks,
  };
};

export default useRealTimeUpdates;
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { propertyOwnerService, PropertyData } from '../../services/propertyOwnerService';
import { PropertyFormSteps } from '../../components/property-owner/PropertyFormSteps';
import { API_BASE_URL } from '../../config/api';

// Form data interface
interface PropertyFormData {
  title: string;
  description: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province: string;
  };
  propertyType: 'apartment' | 'house' | 'studio' | 'room';
  size: string;
  rooms: string;
  bedrooms: string;
  bathrooms: string;
  rent: {
    amount: string;
    deposit: string;
    utilities: string;
    serviceCharges: string;
  };
  features: {
    furnished: boolean;
    interior: 'kaal' | 'gestoffeerd' | 'gemeubileerd';
    parking: boolean;
    balcony: boolean;
    garden: boolean;
    elevator: boolean;
    energyLabel: string;
  };
  policies: {
    petsAllowed: boolean;
    smokingAllowed: boolean;
    studentsAllowed: boolean;
    expatFriendly: boolean;
    minimumIncome: string;
    maximumOccupants: string;
  };
  photos: {
    uri: string;
    name: string;
    type: string;
    isPrimary: boolean;
  }[];
}

export default function AddPropertyScreen() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;
  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    address: {
      street: '',
      houseNumber: '',
      postalCode: '',
      city: '',
      province: '',
    },
    propertyType: 'apartment',
    size: '',
    rooms: '',
    bedrooms: '',
    bathrooms: '',
    rent: {
      amount: '',
      deposit: '',
      utilities: '',
      serviceCharges: '',
    },
    features: {
      furnished: false,
      interior: 'kaal',
      parking: false,
      balcony: false,
      garden: false,
      elevator: false,
      energyLabel: 'C',
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      minimumIncome: '',
      maximumOccupants: '',
    },
    photos: [],
  });

  const updateFormData = (section: keyof PropertyFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...(prev[section] as any),
        [field]: value,
      },
    }));
  };
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1: // Basic Information
        return !!(formData.title && formData.description && formData.propertyType);
      case 2: // Address & Details
        return !!(
          formData.address.street &&
          formData.address.houseNumber &&
          formData.address.postalCode &&
          formData.address.city &&
          formData.size &&
          formData.rooms &&
          formData.bedrooms &&
          formData.bathrooms
        );
      case 3: // Rental Information
        return !!(formData.rent.amount && formData.rent.deposit);
      case 4: // Features & Policies
        return true; // Optional fields
      case 5: // Photos
        return true; // Photos are optional but recommended
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      Alert.alert('Incomplete Information', 'Please fill in all required fields before continuing.');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Photo handling functions
  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'We need camera and photo library permissions to add property photos.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          uri: result.assets[0].uri,
          name: `property_photo_${Date.now()}.jpg`,
          type: 'image/jpeg',
          isPrimary: formData.photos.length === 0, // First photo is primary
        };

        setFormData(prev => ({
          ...prev,
          photos: [...prev.photos, newPhoto],
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const selectFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        const newPhotos = result.assets.map((asset, index) => ({
          uri: asset.uri,
          name: `property_photo_${Date.now()}_${index}.jpg`,
          type: 'image/jpeg',
          isPrimary: formData.photos.length === 0 && index === 0, // First photo is primary if no photos exist
        }));

        setFormData(prev => ({
          ...prev,
          photos: [...prev.photos, ...newPhotos],
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select photos. Please try again.');
    }
  };

  const removePhoto = (index: number) => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setFormData(prev => {
              const newPhotos = prev.photos.filter((_, i) => i !== index);
              // If we removed the primary photo, make the first remaining photo primary
              if (prev.photos[index].isPrimary && newPhotos.length > 0) {
                newPhotos[0].isPrimary = true;
              }
              return { ...prev, photos: newPhotos };
            });
          },
        },
      ]
    );
  };

  const setPrimaryPhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.map((photo, i) => ({
        ...photo,
        isPrimary: i === index,
      })),
    }));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      Alert.alert('Incomplete Information', 'Please fill in all required fields.');
      return;
    }

    setLoading(true);
    try {
      let imageUrls: { url: string; caption: string; isPrimary: boolean }[] = [];

      // Upload images if any
      if (formData.photos.length > 0) {
        console.log('Uploading', formData.photos.length, 'images...');
        const uploadResponse = await propertyOwnerService.uploadPropertyImages(
          'new',
          formData.photos
        );
        
        if (uploadResponse.status === 'success') {
          // Map uploaded images to the expected format
          imageUrls = uploadResponse.data.images.map((img: any, index: number) => ({
            url: `${API_BASE_URL}${img.url}`, // Full URL for the uploaded image
            caption: '',
            isPrimary: formData.photos[index].isPrimary
          }));
        }
      }

      // Transform form data to match backend expectations
      const propertyData: PropertyData = {
        title: formData.title,
        description: formData.description,
        address: formData.address,
        propertyType: formData.propertyType,
        size: parseInt(formData.size) || 0,
        rooms: parseInt(formData.rooms) || 0,
        bedrooms: parseInt(formData.bedrooms) || 0,
        bathrooms: parseInt(formData.bathrooms) || 0,
        rent: {
          amount: parseFloat(formData.rent.amount) || 0,
          currency: 'EUR',
          deposit: parseFloat(formData.rent.deposit) || 0,
          additionalCosts: {
            utilities: parseFloat(formData.rent.utilities) || 0,
            serviceCharges: parseFloat(formData.rent.serviceCharges) || 0,
            parking: 0,
            other: 0,
          },
        },
        features: formData.features,
        policies: {
          ...formData.policies,
          minimumIncome: parseFloat(formData.policies.minimumIncome) || 0,
          maximumOccupants: parseInt(formData.policies.maximumOccupants) || 1,
        },
        status: 'draft',
        images: imageUrls,
        availabilityDate: new Date().toISOString().split('T')[0],
      };

      console.log('Creating property with', imageUrls.length, 'images...');

      const response = await propertyOwnerService.addProperty(propertyData);

      if (response.success) {
        Alert.alert(
          'Success! 🎉',
          'Your property has been added successfully.',
          [
            {
              text: 'OK',
              onPress: () => router.push('/property-owner/dashboard'),
            },
          ]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to add property. Please try again.');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add property. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {Array.from({ length: totalSteps }, (_, index) => (
        <View key={index} style={styles.stepContainer}>
          <View
            style={[
              styles.stepCircle,
              index + 1 <= currentStep ? styles.stepCircleActive : styles.stepCircleInactive,
            ]}
          >
            <Text
              style={[
                styles.stepText,
                index + 1 <= currentStep ? styles.stepTextActive : styles.stepTextInactive,
              ]}
            >
              {index + 1}
            </Text>
          </View>
          {index < totalSteps - 1 && (
            <View
              style={[
                styles.stepLine,
                index + 1 < currentStep ? styles.stepLineActive : styles.stepLineInactive,
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Basic Information</Text>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Property Title *</Text>
        <TextInput
          style={styles.input}
          value={formData.title}
          onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
          placeholder="e.g., Modern Apartment in City Center"
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Description *</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.description}
          onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
          placeholder="Describe your property, its features, and location..."
          placeholderTextColor="#999"
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Property Type *</Text>
        <View style={styles.propertyTypeContainer}>
          {(['apartment', 'house', 'studio', 'room'] as const).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.propertyTypeButton,
                formData.propertyType === type && styles.propertyTypeButtonActive,
              ]}
              onPress={() => setFormData(prev => ({ ...prev, propertyType: type }))}
            >
              <Text
                style={[
                  styles.propertyTypeText,
                  formData.propertyType === type && styles.propertyTypeTextActive,
                ]}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Address & Details</Text>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 3 }]}>
          <Text style={styles.label}>Street *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.street}
            onChangeText={(value) => updateFormData('address', 'street', value)}
            placeholder="Street name"
            placeholderTextColor="#999"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Number *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.houseNumber}
            onChangeText={(value) => updateFormData('address', 'houseNumber', value)}
            placeholder="123"
            placeholderTextColor="#999"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Postal Code *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.postalCode}
            onChangeText={(value) => updateFormData('address', 'postalCode', value)}
            placeholder="1234AB"
            placeholderTextColor="#999"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 2, marginLeft: 12 }]}>
          <Text style={styles.label}>City *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.city}
            onChangeText={(value) => updateFormData('address', 'city', value)}
            placeholder="Amsterdam"
            placeholderTextColor="#999"
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Province</Text>
        <TextInput
          style={styles.input}
          value={formData.address.province}
          onChangeText={(value) => updateFormData('address', 'province', value)}
          placeholder="Noord-Holland"
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Size (m²) *</Text>
          <TextInput
            style={styles.input}
            value={formData.size}
            onChangeText={(value) => setFormData(prev => ({ ...prev, size: value }))}
            placeholder="75"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Rooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.rooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, rooms: value }))}
            placeholder="3"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Bedrooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.bedrooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, bedrooms: value }))}
            placeholder="2"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Bathrooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.bathrooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, bathrooms: value }))}
            placeholder="1"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );
  const renderStep3 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Rental Information</Text>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Monthly Rent (€) *</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.amount}
            onChangeText={(value) => updateFormData('rent', 'amount', value)}
            placeholder="1200"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Deposit (€) *</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.deposit}
            onChangeText={(value) => updateFormData('rent', 'deposit', value)}
            placeholder="2400"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Utilities (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.utilities}
            onChangeText={(value) => updateFormData('rent', 'utilities', value)}
            placeholder="150"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Service Charges (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.serviceCharges}
            onChangeText={(value) => updateFormData('rent', 'serviceCharges', value)}
            placeholder="50"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Features & Policies</Text>

      <Text style={styles.sectionTitle}>Property Features</Text>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Furnished</Text>
        <Switch
          value={formData.features.furnished}
          onValueChange={(value) => updateFormData('features', 'furnished', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Parking</Text>
        <Switch
          value={formData.features.parking}
          onValueChange={(value) => updateFormData('features', 'parking', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Balcony</Text>
        <Switch
          value={formData.features.balcony}
          onValueChange={(value) => updateFormData('features', 'balcony', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Garden</Text>
        <Switch
          value={formData.features.garden}
          onValueChange={(value) => updateFormData('features', 'garden', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Elevator</Text>
        <Switch
          value={formData.features.elevator}
          onValueChange={(value) => updateFormData('features', 'elevator', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <Text style={styles.sectionTitle}>Rental Policies</Text>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Pets Allowed</Text>
        <Switch
          value={formData.policies.petsAllowed}
          onValueChange={(value) => updateFormData('policies', 'petsAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Smoking Allowed</Text>
        <Switch
          value={formData.policies.smokingAllowed}
          onValueChange={(value) => updateFormData('policies', 'smokingAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Students Welcome</Text>
        <Switch
          value={formData.policies.studentsAllowed}
          onValueChange={(value) => updateFormData('policies', 'studentsAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Expat Friendly</Text>
        <Switch
          value={formData.policies.expatFriendly}
          onValueChange={(value) => updateFormData('policies', 'expatFriendly', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Min. Income (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.policies.minimumIncome}
            onChangeText={(value) => updateFormData('policies', 'minimumIncome', value)}
            placeholder="3600"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Max. Occupants</Text>
          <TextInput
            style={styles.input}
            value={formData.policies.maximumOccupants}
            onChangeText={(value) => updateFormData('policies', 'maximumOccupants', value)}
            placeholder="2"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderStep5 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Property Photos</Text>
      <Text style={styles.sectionSubtitle}>Add photos to showcase your property</Text>

      {/* Photo Actions */}
      <View style={styles.photoActions}>
        <TouchableOpacity style={styles.photoActionButton} onPress={takePhoto}>
          <Ionicons name="camera" size={24} color="#007AFF" />
          <Text style={styles.photoActionText}>Take Photo</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.photoActionButton} onPress={selectFromGallery}>
          <Ionicons name="images" size={24} color="#007AFF" />
          <Text style={styles.photoActionText}>Choose from Gallery</Text>
        </TouchableOpacity>
      </View>

      {/* Photo Grid */}
      {formData.photos.length > 0 && (
        <View style={styles.photoGrid}>
          <Text style={styles.sectionTitle}>Selected Photos ({formData.photos.length})</Text>
          <View style={styles.photoList}>
            {formData.photos.map((item, index) => (
              <View key={index} style={styles.photoItem}>
                <Image source={{ uri: item.uri }} style={styles.photoImage} />

                {/* Primary Photo Badge */}
                {item.isPrimary && (
                  <View style={styles.primaryBadge}>
                    <Text style={styles.primaryBadgeText}>Primary</Text>
                  </View>
                )}

                {/* Photo Actions */}
                <View style={styles.photoItemActions}>
                  {!item.isPrimary && (
                    <TouchableOpacity
                      style={styles.photoActionIcon}
                      onPress={() => setPrimaryPhoto(index)}
                    >
                      <Ionicons name="star-outline" size={20} color="#FFF" />
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={[styles.photoActionIcon, styles.removeAction]}
                    onPress={() => removePhoto(index)}
                  >
                    <Ionicons name="trash-outline" size={20} color="#FFF" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Photo Tips */}
      <View style={styles.photoTips}>
        <Text style={styles.tipsTitle}>📸 Photo Tips</Text>
        <Text style={styles.tipText}>• Take photos in good lighting</Text>
        <Text style={styles.tipText}>• Show different rooms and angles</Text>
        <Text style={styles.tipText}>• Include exterior and interior views</Text>
        <Text style={styles.tipText}>• First photo will be the main listing photo</Text>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    const stepProps = {
      formData,
      setFormData,
      updateFormData,
      takePhoto,
      selectFromGallery,
      removePhoto,
      setPrimaryPhoto,
    };

    switch (currentStep) {
      case 1:
        return <PropertyFormSteps.Step1 {...stepProps} />;
      case 2:
        return <PropertyFormSteps.Step2 {...stepProps} />;
      case 3:
        return <PropertyFormSteps.Step3 {...stepProps} />;
      case 4:
        return <PropertyFormSteps.Step4 {...stepProps} />;
      case 5:
        return <PropertyFormSteps.Step5 {...stepProps} />;
      default:
        return <PropertyFormSteps.Step1 {...stepProps} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Property</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Form Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        {currentStep > 1 && (
          <TouchableOpacity style={styles.secondaryButton} onPress={prevStep}>
            <Text style={styles.secondaryButtonText}>Previous</Text>
          </TouchableOpacity>
        )}

        <View style={styles.buttonSpacer} />

        {currentStep < totalSteps ? (
          <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
            <Text style={styles.primaryButtonText}>Next</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.primaryButton, loading && styles.primaryButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.primaryButtonText}>Add Property</Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  placeholder: {
    width: 40,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    marginBottom: 1,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  stepCircleActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  stepCircleInactive: {
    backgroundColor: '#FFFFFF',
    borderColor: '#E5E5E5',
  },
  stepText: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepTextActive: {
    color: '#FFFFFF',
  },
  stepTextInactive: {
    color: '#999999',
  },
  stepLine: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  stepLineActive: {
    backgroundColor: '#007AFF',
  },
  stepLineInactive: {
    backgroundColor: '#E5E5E5',
  },
  scrollView: {
    flex: 1,
  },
  stepContent: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    margin: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginTop: 20,
    marginBottom: 12,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1A1A1A',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  propertyTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  propertyTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: '#FFFFFF',
  },
  propertyTypeButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  propertyTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  propertyTypeTextActive: {
    color: '#FFFFFF',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333333',
    flex: 1,
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  buttonSpacer: {
    flex: 1,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  primaryButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    minWidth: 100,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Photo styles
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  photoActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  photoActionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    minWidth: 120,
  },
  photoActionText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  photoGrid: {
    marginBottom: 24,
  },
  photoList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingTop: 12,
    justifyContent: 'space-between',
  },
  photoItem: {
    width: '48%',
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#F0F0F0',
    position: 'relative',
  },
  photoImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  primaryBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  primaryBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  photoItemActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    gap: 8,
  },
  photoActionIcon: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeAction: {
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
  },
  photoTips: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
    lineHeight: 20,
  },
});
import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors to match the rest of the app
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Enhanced Header Component to match dashboard
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Contract Review</Text>
            <Text style={styles.headerSubtitle}>AI-powered legal analysis</Text>
          </View>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

export default function ContractReviewScreen() {
  const router = useRouter();
  const [contractText, setContractText] = useState("");
  const [analysisResult, setAnalysisResult] = useState("");
  const [isLoadingAI, setIsLoadingAI] = useState(false);

  const analyzeContract = async () => {
    setIsLoadingAI(true);
    setAnalysisResult("Analyzing your contract for legal risks...");

    // Simulate API call to AI layer
    try {
      // In a real app, you would call an AI API here
      await new Promise((resolve) => setTimeout(resolve, 3000)); // Simulate delay

      const analysisText = `Contract Analysis Results:

KEY TERMS IDENTIFIED:
• Rental Period: Standard 12-month lease agreement
• Monthly Rent: As specified in the contract
• Security Deposit: Typically 1-2 months rent
• Notice Period: Usually 1 month for termination

POTENTIAL CONCERNS:
⚠️ Check for excessive penalty clauses
⚠️ Verify maintenance responsibilities are clearly defined
⚠️ Ensure deposit return conditions are fair
⚠️ Review early termination clauses

RECOMMENDATIONS:
✅ The contract appears to follow Dutch rental law standards
✅ Most terms seem reasonable for the Dutch market
✅ Consider negotiating any unclear maintenance responsibilities
✅ Ensure all verbal agreements are documented

NEXT STEPS:
• Have a legal professional review if you have concerns
• Clarify any unclear terms with the landlord
• Document the property condition before signing
• Keep copies of all communications

This analysis is for informational purposes only and does not constitute legal advice.`;

      setAnalysisResult(analysisText);
    } catch (error) {
      console.error("Error analyzing contract:", error);
      setAnalysisResult(
        "Error analyzing contract. Please check your connection."
      );
    } finally {
      setIsLoadingAI(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contractContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={styles.contentCard}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <View style={styles.titleSection}>
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={[THEME.accent, THEME.secondary]}
                style={styles.iconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Ionicons name="document-text" size={24} color={THEME.light} />
              </LinearGradient>
            </View>
            <View style={styles.titleContent}>
              <Text style={styles.contractTitle}>AI Contract Analysis</Text>
              <Text style={styles.contractSubtitle}>
                Paste your rental contract text below for an AI-powered legal review
              </Text>
            </View>
          </View>

          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Contract Text</Text>
            <View style={styles.textAreaContainer}>
              <TextInput
                style={styles.textArea}
                placeholder="Paste your contract text here..."
                placeholderTextColor={THEME.gray}
                value={contractText}
                onChangeText={setContractText}
                multiline
                numberOfLines={12}
                textAlignVertical="top"
              />
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.analyzeButton,
              (!contractText || isLoadingAI) && styles.disabledButton,
            ]}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              analyzeContract();
            }}
            disabled={!contractText || isLoadingAI}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={(!contractText || isLoadingAI) 
                ? [THEME.gray, THEME.gray] 
                : [THEME.accent, THEME.secondary]
              }
              style={styles.analyzeButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              {isLoadingAI ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={THEME.light} />
                  <Text style={styles.analyzeButtonText}>Analyzing...</Text>
                </View>
              ) : (
                <View style={styles.buttonContent}>
                  <Ionicons name="flash" size={20} color={THEME.light} />
                  <Text style={styles.analyzeButtonText}>Analyze Contract</Text>
                </View>
              )}
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {analysisResult && (
          <Animated.View
            style={styles.analysisCard}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.analysisHeader}>
              <View style={styles.analysisIconContainer}>
                <Ionicons name="checkmark-circle" size={24} color={THEME.success} />
              </View>
              <Text style={styles.analysisTitle}>Analysis Complete</Text>
            </View>
            <View style={styles.analysisContent}>
              <Text style={styles.analysisText}>{analysisResult}</Text>
            </View>
          </Animated.View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.light,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerSpacer: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
  },
  contractContainer: {
    padding: 20,
  },
  contentCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  titleSection: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 32,
  },
  iconContainer: {
    marginRight: 16,
  },
  iconGradient: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  titleContent: {
    flex: 1,
    paddingTop: 4,
  },
  contractTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.dark,
    marginBottom: 8,
  },
  contractSubtitle: {
    fontSize: 16,
    color: THEME.gray,
    lineHeight: 24,
  },
  inputSection: {
    marginBottom: 32,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 12,
  },
  textAreaContainer: {
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    backgroundColor: THEME.light,
    overflow: 'hidden',
  },
  textArea: {
    height: 200,
    padding: 20,
    fontSize: 16,
    color: THEME.dark,
    textAlignVertical: "top",
    lineHeight: 24,
  },
  analyzeButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  analyzeButtonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  analyzeButtonText: {
    color: THEME.light,
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  disabledButton: {
    opacity: 0.6,
  },
  analysisCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 24,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  analysisHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  analysisIconContainer: {
    marginRight: 12,
  },
  analysisTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: THEME.dark,
  },
  analysisContent: {
    backgroundColor: 'rgba(16, 185, 129, 0.05)',
    borderRadius: 12,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: THEME.success,
  },
  analysisText: {
    fontSize: 15,
    color: THEME.dark,
    lineHeight: 24,
  },
});

import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Dimensions,
  Linking,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AutonomousStatusDashboard } from '../components/autonomous/AutonomousStatusDashboard';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useAuthStore } from '../store/authStore';
import { useAIStore } from '../store/aiStore';
import {
  autoApplicationService,
  AutoApplicationStats,
  ApplicationQueue,
  ApplicationResult,
} from '../services/autoApplicationService';

const { width } = Dimensions.get('window');

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Auto-Application Dashboard</Text>
        </View>

        <TouchableOpacity
          onPress={() => router.push('/auto-application-settings')}
          style={styles.settingsButton}
          activeOpacity={0.8}
        >
          <Ionicons name="settings-outline" size={24} color="#ffffff" />
        </TouchableOpacity>
      </Animated.View>
    </LinearGradient>
  );
};

// Stats Card Component
const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
}: {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  trend?: { value: number; isPositive: boolean };
}) => (
  <Animated.View
    style={[styles.statsCard, { borderLeftColor: color }]}
    entering={FadeInUp.duration(600)}
  >
    <View style={styles.statsCardHeader}>
      <View style={[styles.statsIcon, { backgroundColor: color }]}>
        <Ionicons name={icon as any} size={20} color="#ffffff" />
      </View>
      <View style={styles.statsContent}>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsTitle}>{title}</Text>
        {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
      </View>
      {trend && (
        <View style={styles.trendContainer}>
          <Ionicons
            name={trend.isPositive ? "trending-up" : "trending-down"}
            size={16}
            color={trend.isPositive ? THEME.success : THEME.danger}
          />
          <Text style={[
            styles.trendText,
            { color: trend.isPositive ? THEME.success : THEME.danger }
          ]}>
            {Math.abs(trend.value)}%
          </Text>
        </View>
      )}
    </View>
  </Animated.View>
);

// Queue Item Component
const QueueItem = ({
  item,
  onRemove,
  onUpdatePriority,
}: {
  item: ApplicationQueue;
  onRemove: (id: string) => void;
  onUpdatePriority: (id: string, priority: number) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return THEME.warning;
      case 'processing': return THEME.primary;
      case 'paused': return '#9333ea'; // Purple for paused
      case 'retrying': return '#f59e0b'; // Amber for retrying
      case 'completed': return THEME.success;
      case 'failed': return THEME.danger;
      case 'cancelled': return THEME.gray;
      default: return THEME.gray;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'processing': return 'sync-outline';
      case 'paused': return 'pause-circle-outline';
      case 'retrying': return 'refresh-outline';
      case 'completed': return 'checkmark-circle-outline';
      case 'failed': return 'close-circle-outline';
      case 'cancelled': return 'ban-outline';
      default: return 'help-circle-outline';
    }
  };

  // Display attempts more intuitively: if item is not pending and attempts is 0/undefined, show 1
  const attemptsUsed = (() => {
    const raw = Number(item.attempts ?? 0);
    if (['processing', 'completed', 'failed', 'cancelled', 'paused', 'retrying'].includes(item.status) && raw === 0) {
      return 1;
    }
    return isNaN(raw) ? 0 : raw;
  })();
  const attemptsMax = Number(item.maxAttempts ?? 3);

  return (
    <Animated.View
      style={styles.queueItem}
      entering={FadeInDown.duration(400)}
    >
      <View style={styles.queueItemHeader}>
        <View style={styles.queueItemTitle}>
          <Text style={styles.queueItemName} numberOfLines={1}>
            {item.listingTitle}
          </Text>
          <View style={styles.queueItemMeta}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Ionicons name={getStatusIcon(item.status) as any} size={12} color="#ffffff" />
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
            <Text style={styles.priorityText}>Priority: {item.priority}</Text>
          </View>
        </View>

        <View style={styles.queueItemActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.min(10, item.priority + 1))}
          >
            <Ionicons name="arrow-up" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.max(1, item.priority - 1))}
          >
            <Ionicons name="arrow-down" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => onRemove(item._id)}
          >
            <Ionicons name="trash-outline" size={16} color={THEME.danger} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.queueItemDetails}>
        <Text style={styles.queueItemDetail}>
          Scheduled: {item.scheduledAt ? new Date(item.scheduledAt).toLocaleString() : 'Invalid Date'}
        </Text>
        <Text style={styles.queueItemDetail}>
          Attempts: {attemptsUsed}/{attemptsMax}
        </Text>

      </View>

      {item.errorMessage && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={14} color={THEME.danger} />
          <Text style={styles.errorText}>{item.errorMessage}</Text>
        </View>
      )}
    </Animated.View>
  );
};

// Filter Picker Component
const FilterPicker = ({
  options,
  selectedValue,
  onValueChange,
  label
}: {
  options: Array<{ label: string; value: string }>;
  selectedValue: string;
  onValueChange: (value: string) => void;
  label: string;
}) => (
  <View style={styles.filterContainer}>
    <Text style={styles.filterLabel}>{label}</Text>
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.filterOptions}
    >
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.filterOption,
            selectedValue === option.value && styles.filterOptionActive
          ]}
          onPress={() => {
            onValueChange(option.value);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
        >
          <Text style={[
            styles.filterOptionText,
            selectedValue === option.value && styles.filterOptionTextActive
          ]}>
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  </View>
);

// Helper: derive a human-readable title from various possible fields
const deriveListingTitle = (raw: any): string => {
  try {
    // 1) Preferred explicit fields
    const direct = raw?.listingTitle || raw?.title || raw?.name;
    if (direct && typeof direct === 'string') return direct;

    // 2) Common snapshot shapes
    const snap = raw?.listingSnapshot || raw?.snapshot || raw?.listing;
    const snapTitle = snap?.title || snap?.heading || snap?.address || snap?.name;
    if (snapTitle && typeof snapTitle === 'string') return snapTitle;

    // 3) Derive from URL
    const url: string | undefined = snap?.url || raw?.listingUrl;
    if (url && typeof url === 'string') {
      try {
        const u = new URL(url);
        const segments = u.pathname.split('/').filter(Boolean);
        // Look for the last non-numeric, non-id-like segment
        let candidate = segments.reverse().find((seg) => !/^\d+$/.test(seg));
        if (!candidate && segments.length > 0) candidate = segments[0];
        if (candidate) {
          const decoded = decodeURIComponent(candidate)
            .replace(/-/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
          // Title case
          const titled = decoded
            .split(' ')
            .map((w) => (w ? w.charAt(0).toUpperCase() + w.slice(1) : w))
            .join(' ');
          // Remove common prefixes that aren’t helpful
          return titled.replace(/^Appartement\s+|^Huis\s+/i, '').trim() || 'Unknown Property';
        }
      } catch {}
    }

    // 4) Fallback
    return 'Unknown Property';
  } catch {
    return 'Unknown Property';
  }
};

// Utility function to safely process application result data
const processApplicationResult = (result: any): ApplicationResult | null => {
  try {
    // Validate required fields
    if (!result || !result._id) {
      console.warn('Missing required fields in result:', result);
      return null;
    }

    // Ensure all required fields have fallback values
    return {
      ...result,
      _id: result._id,
      listingTitle: deriveListingTitle(result),
      status: result.status || 'unknown',
      submittedAt: result.submittedAt || result.createdAt || new Date(),
      userId: result.userId || '',
      queueId: result.queueId || '',
      listingId: result.listingId || '',
      listingUrl: result.listingUrl || result.listingSnapshot?.url || '',
      responseTime: result.responseTime || 0,
      formData: result.formData || {},
      screenshots: result.screenshots || [],
      metadata: result.metadata || {},
      createdAt: result.createdAt || result.submittedAt || new Date(),
      // Preserve listingSnapshot with all its data
      listingSnapshot: result.listingSnapshot ? {
        title: result.listingSnapshot.title,
        address: result.listingSnapshot.address,
        price: result.listingSnapshot.price,
        rooms: result.listingSnapshot.rooms,
        size: result.listingSnapshot.size,
        propertyType: result.listingSnapshot.propertyType,
        url: result.listingSnapshot.url || result.listingUrl,
        landlord: result.listingSnapshot.landlord,
        description: result.listingSnapshot.description,
        availableFrom: result.listingSnapshot.availableFrom,
        applicationDeadline: result.listingSnapshot.applicationDeadline,
        ...result.listingSnapshot
      } : undefined,
      // Handle nested objects safely
      metrics: result.metrics ? {
        successProbability: result.metrics.successProbability || 0,
        processingTime: result.metrics.processingTime || 0,
        ...result.metrics
      } : undefined,
      response: result.response ? {
        responseTime: result.response.responseTime || result.responseTime || 0,
        ...result.response
      } : undefined,
      errorDetails: result.errorDetails || undefined,
      landlordResponse: result.landlordResponse || undefined
    };
  } catch (error) {
    console.error('Error processing application result:', error, result);
    return null;
  }
};

// Score helpers
const normalizeScore = (val: any): number | null => {
  if (val == null) return null;
  const n = Number(val);
  if (Number.isNaN(n)) return null;
  if (n <= 1) return Math.round(n * 100);
  return Math.round(Math.max(0, Math.min(100, n)));
};

const getResultScoreText = (result: any): string => {
  const s1 = normalizeScore(result?.successScore);
  const s2 = normalizeScore(result?.metrics?.successProbability);
  const s3 = normalizeScore(result?.aiContent?.confidence);
  let chosen: number | null = null;
  if (s1 != null) chosen = s1;
  else if (s2 != null) chosen = (s2 === 50 && s3 != null) ? s3 : s2; // Prefer AI confidence over placeholder 50
  else if (s3 != null) chosen = s3;
  return chosen != null ? `${chosen}%` : 'N/A';
};

// Recent Result Component
const RecentResult = ({ result }: { result: ApplicationResult }) => {
  // Early validation
  if (!result || typeof result !== 'object') {
    return (
      <View style={styles.enhancedResultItem}>
        <View style={styles.enhancedErrorContainer}>
          <View style={styles.errorHeader}>
            <Ionicons name="alert-circle" size={16} color={THEME.danger} />
            <Text style={styles.errorHeaderText}>No Data</Text>
          </View>
          <Text style={styles.enhancedErrorText}>Result data is missing or invalid</Text>
        </View>
      </View>
    );
  }

  try {
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'success':
      case 'submitted': // Treat submitted as successful
        return {
          color: THEME.success,
          icon: 'checkmark-circle',
          text: 'Submitted Successfully',
          gradient: [THEME.success, '#059669']
        };
      case 'failed':
        return {
          color: THEME.danger,
          icon: 'close-circle',
          text: 'Submission Failed',
          gradient: [THEME.danger, '#dc2626']
        };
      case 'cancelled':
        return {
          color: THEME.warning,
          icon: 'stop-circle',
          text: 'Cancelled',
          gradient: [THEME.warning, '#d97706']
        };
      default:
        return {
          color: THEME.gray,
          icon: 'help-circle',
          text: 'Unknown Status',
          gradient: [THEME.gray, '#4b5563']
        };
    }
  };

  const statusInfo = getStatusInfo(result?.status || 'unknown');
  
  // Safely get formatted data
  let timeAgo = 'Unknown';
  try {
    const formatted = autoApplicationService.formatApplicationResult(result);
    timeAgo = formatted.timeAgo;
  } catch (error) {
    console.warn('Error formatting result:', error);
    // Fallback time calculation
    if (result.submittedAt) {
      const now = new Date();
      const submitted = new Date(result.submittedAt);
      const diffMs = now.getTime() - submitted.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      if (diffMins < 1) timeAgo = "Just now";
      else if (diffMins < 60) timeAgo = `${diffMins}m ago`;
      else if (diffHours < 24) timeAgo = `${diffHours}h ago`;
      else timeAgo = `${diffDays}d ago`;
    }
  }

  const successScore = getResultScoreText(result);
  const responseTime = typeof (result.response?.responseTime ?? result.responseTime) === 'number'
    ? `${result.response?.responseTime ?? result.responseTime}ms`
    : null;

  return (
    <Animated.View
      style={styles.enhancedResultItem}
      entering={FadeInDown.duration(400)}
    >
      {/* Header Section */}
      <View style={styles.enhancedResultHeader}>
        <LinearGradient
          colors={statusInfo.gradient as any}
          style={styles.statusIconContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons name={statusInfo.icon as any} size={20} color={THEME.light} />
        </LinearGradient>
        
        <View style={styles.enhancedResultContent}>
          <Text style={styles.enhancedResultTitle} numberOfLines={2}>
            {result?.listingTitle || deriveListingTitle(result) || 'Unknown Property'}
          </Text>
          <View style={styles.resultStatusRow}>
            <View style={[styles.statusBadge, { backgroundColor: `${statusInfo.color}15`, borderColor: `${statusInfo.color}40` }]}>
              <Text style={[styles.statusBadgeText, { color: statusInfo.color }]}>
                {statusInfo.text}
              </Text>
            </View>
            <View style={styles.timeContainer}>
              <Ionicons name="time-outline" size={12} color={THEME.gray} />
              <Text style={styles.enhancedResultTime}>{timeAgo}</Text>
            </View>
          </View>
        </View>

        {/* Score Badge */}
        {successScore !== 'N/A' && (
          <View style={styles.scoreBadgeContainer}>
            <Text style={styles.scoreBadgeText}>{successScore}</Text>
            <Text style={styles.scoreBadgeLabel}>Score</Text>
          </View>
        )}
      </View>

      {/* Metrics Row */}
      <View style={styles.metricsRow}>
        <View style={styles.metricItem}>
          <Ionicons name="calendar-outline" size={14} color={THEME.gray} />
          <Text style={styles.metricText}>
            {result?.submittedAt ? (() => {
              try {
                return new Date(result.submittedAt).toLocaleDateString();
              } catch (e) {
                return 'Invalid date';
              }
            })() : 'Unknown date'}
          </Text>
        </View>
        
        {responseTime && (
          <View style={styles.metricItem}>
            <Ionicons name="speedometer-outline" size={14} color={THEME.gray} />
            <Text style={styles.metricText}>{responseTime}</Text>
          </View>
        )}
        
        {result.metrics?.formComplexity && (
          <View style={styles.metricItem}>
            <Ionicons name="layers-outline" size={14} color={THEME.gray} />
            <Text style={styles.metricText}>
              {result.metrics.formComplexity.toString().replace('_', ' ')}
            </Text>
          </View>
        )}
      </View>

      {/* Error Section */}
      {result.errorDetails && (
        <View style={styles.enhancedErrorContainer}>
          <View style={styles.errorHeader}>
            <Ionicons name="alert-circle" size={16} color={THEME.danger} />
            <Text style={styles.errorHeaderText}>Error Details</Text>
          </View>
          <Text style={styles.enhancedErrorText}>
            {result.errorDetails?.message || 'Unknown error occurred'}
          </Text>
          {result.errorDetails?.captchaDetected && (
            <View style={styles.errorTag}>
              <Text style={styles.errorTagText}>CAPTCHA Detected</Text>
            </View>
          )}
          {result.errorDetails?.blockingDetected && (
            <View style={styles.errorTag}>
              <Text style={styles.errorTagText}>Blocking Detected</Text>
            </View>
          )}
        </View>
      )}

      {/* Status-specific Instructions */}
      {(result?.status === 'success' || result?.status === 'failed') && (
        <View style={styles.checkEmailContainer}>
          <View style={styles.emailHeader}>
            <Ionicons 
              name={result?.status === 'success' ? 'mail' : 'alert-circle'} 
              size={16} 
              color={result?.status === 'success' ? THEME.primary : THEME.danger} 
            />
            <Text style={styles.emailHeaderText}>
              {result?.status === 'success' ? 'Check Your Email' : 'Application Status'}
            </Text>
            <View style={[
              styles.successBadge,
              { backgroundColor: result?.status === 'success' ? THEME.success : THEME.danger }
            ]}>
              <Text style={styles.successBadgeText}>
                {result?.status === 'success' ? 'SUBMITTED' : 'FAILED'}
              </Text>
            </View>
          </View>
          
          <Text style={styles.emailInstructionText}>
            {result?.status === 'success' 
              ? `Your application for ${result?.listingTitle || 'this property'} was successfully submitted! Check your inbox for landlord responses and follow-up communications.`
              : `Your application for ${result?.listingTitle || 'this property'} could not be submitted automatically. You may need to apply manually through the listing website.`
            }
          </Text>
          
          <View style={styles.emailTips}>
            {result?.status === 'success' ? (
              <>
                <View style={styles.emailTip}>
                  <Ionicons name="checkmark-circle-outline" size={14} color={THEME.success} />
                  <Text style={styles.emailTipText}>Check both inbox and spam folder</Text>
                </View>
                <View style={styles.emailTip}>
                  <Ionicons name="time-outline" size={14} color={THEME.warning} />
                  <Text style={styles.emailTipText}>Responses typically arrive within 1-3 days</Text>
                </View>
              </>
            ) : (
              <>
                <View style={styles.emailTip}>
                  <Ionicons name="globe-outline" size={14} color={THEME.primary} />
                  <Text style={styles.emailTipText}>Visit the original listing website to apply manually</Text>
                </View>
                <View style={styles.emailTip}>
                  <Ionicons name="refresh-outline" size={14} color={THEME.secondary} />
                  <Text style={styles.emailTipText}>System will try again automatically if enabled</Text>
                </View>
              </>
            )}
          </View>
          
          <TouchableOpacity 
            style={styles.openEmailButton}
            onPress={async () => {
              if (result?.status === 'success') {
                try {
                  // Try to open the default email app
                  const emailUrl = 'mailto:';
                  const canOpen = await Linking.canOpenURL(emailUrl);
                  if (canOpen) {
                    await Linking.openURL(emailUrl);
                  } else {
                    Alert.alert(
                      'Email App Not Found',
                      'Please check your inbox manually in your preferred email app.',
                      [{ text: 'OK' }]
                    );
                  }
                } catch (error) {
                  console.warn('Error opening email app:', error);
                  Alert.alert(
                    'Cannot Open Email',
                    'Please check your inbox manually in your email app.',
                    [{ text: 'OK' }]
                  );
                }
              } else {
                // For failed applications, try to open the listing URL
                try {
                  // Try multiple sources for the listing URL with fallbacks
                  const listingUrl = 
                    result?.listingSnapshot?.url ||
                    result?.listingUrl ||
                    (result as any)?.url ||
                    null;
                  
                  console.log('🔗 Attempting to open listing URL:', {
                    listingId: result?._id,
                    listingTitle: result?.listingTitle,
                    availableSources: {
                      'listingSnapshot.url': result?.listingSnapshot?.url,
                      'listingUrl': result?.listingUrl,
                      'url': (result as any)?.url,
                    },
                    selectedUrl: listingUrl
                  });
                  
                  if (listingUrl && listingUrl.trim() !== '') {
                    const canOpen = await Linking.canOpenURL(listingUrl);
                    if (canOpen) {
                      console.log('✅ Opening listing URL:', listingUrl);
                      await Linking.openURL(listingUrl);
                    } else {
                      console.warn('❌ Cannot open URL:', listingUrl);
                      Alert.alert(
                        'Cannot Open Link',
                        'Please visit the listing website manually to apply.',
                        [{ text: 'OK' }]
                      );
                    }
                  } else {
                    console.warn('❌ No listing URL found in result:', {
                      resultKeys: Object.keys(result || {}),
                      listingSnapshot: result?.listingSnapshot ? Object.keys(result.listingSnapshot) : null
                    });
                    Alert.alert(
                      'No Link Available',
                      'The listing URL is not available. Please search for the property manually.',
                      [{ text: 'OK' }]
                    );
                  }
                } catch (error) {
                  console.warn('Error opening listing URL:', error);
                  Alert.alert(
                    'Cannot Open Link',
                    'Please visit the listing website manually to apply.',
                    [{ text: 'OK' }]
                  );
                }
              }
            }}
          >
            <Ionicons 
              name={result?.status === 'success' ? 'mail-open' : 'globe'} 
              size={16} 
              color={THEME.primary} 
            />
            <Text style={styles.openEmailText}>
              {result?.status === 'success' ? 'Open Email App' : 'View Listing'}
            </Text>
            <Ionicons name="chevron-forward" size={14} color={THEME.gray} />
          </TouchableOpacity>
        </View>
      )}
    </Animated.View>
  );
  
  } catch (error) {
    console.error('Error rendering RecentResult:', error);
    return (
      <View style={styles.enhancedResultItem}>
        <View style={styles.enhancedErrorContainer}>
          <View style={styles.errorHeader}>
            <Ionicons name="alert-circle" size={16} color={THEME.danger} />
            <Text style={styles.errorHeaderText}>Render Error</Text>
          </View>
          <Text style={styles.enhancedErrorText}>
            Unable to display this result. Please try refreshing.
          </Text>
        </View>
      </View>
    );
  }
};

export default function AutoApplicationDashboardScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { autonomousStatus, fetchAutonomousStatus } = useAIStore();

  // State
  const [stats, setStats] = useState<AutoApplicationStats | null>(null);
  const [queue, setQueue] = useState<ApplicationQueue[]>([]);
  const [recentResults, setRecentResults] = useState<ApplicationResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { tab } = useLocalSearchParams<{ tab?: string }>();
  const initialTab = (tab === 'autonomous' ? 'autonomous' : 'overview') as 'overview' | 'queue' | 'results' | 'autonomous';
  const [activeTab, setActiveTab] = useState<'overview' | 'queue' | 'results' | 'autonomous'>(initialTab);
  const [pausingQueue, setPausingQueue] = useState(false);
  const [resumingQueue, setResumingQueue] = useState(false);
  
  // Filter states
  const [queueFilter, setQueueFilter] = useState<string>('all');
  const [resultsFilter, setResultsFilter] = useState<string>('submitted');

  // Load data
  const loadData = useCallback(async (isRefresh = false) => {
    if (!user?.id) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const [statsResponse, queueResponse, resultsResponse] = await Promise.allSettled([
        autoApplicationService.getStats(user.id),
        autoApplicationService.getQueue(user.id),
        autoApplicationService.getResults(user.id, 1, 10), // Get all results, filter on frontend
      ]);

      if (statsResponse.status === 'fulfilled' && statsResponse.value.success) {
        setStats(statsResponse.value.data || null);
      }

      if (queueResponse.status === 'fulfilled' && queueResponse.value.success) {
        setQueue(queueResponse.value.data || []);
      }

      if (resultsResponse.status === 'fulfilled' && resultsResponse.value.success) {
        console.log('🔍 DEBUG: Results API response:', {
          success: resultsResponse.value.success,
          data: resultsResponse.value.data,
          rawResults: resultsResponse.value.data?.results,
          resultsCount: resultsResponse.value.data?.results?.length || 0
        });
        
        const rawResults = resultsResponse.value.data?.results || [];
        console.log('🔍 DEBUG: Raw results from API:', rawResults);
        
        // Process and validate each result - no filtering here, we'll filter in UI
        const processedResults = rawResults
          .map((rawResult: any, index: number) => {
            console.log(`🔍 DEBUG: Processing result ${index}:`, {
              id: rawResult._id,
              status: rawResult.status,
              listingTitle: rawResult.listingTitle,
              listingUrl: rawResult.listingUrl,
              listingSnapshot: rawResult.listingSnapshot ? {
                title: rawResult.listingSnapshot.title,
                url: rawResult.listingSnapshot.url,
                hasUrl: !!rawResult.listingSnapshot.url
              } : 'missing',
              hasDirectUrl: !!rawResult.listingUrl,
              allKeys: Object.keys(rawResult)
            });
            const processed = processApplicationResult(rawResult);
            if (!processed) {
              console.warn(`Failed to process result ${index}:`, rawResult);
            } else {
              // Debug the processed result URLs
              console.log(`✅ Processed result ${index} URLs:`, {
                id: processed._id,
                listingUrl: processed.listingUrl,
                snapshotUrl: processed.listingSnapshot?.url,
                finalUrl: processed.listingSnapshot?.url || processed.listingUrl,
                hasAnyUrl: !!(processed.listingSnapshot?.url || processed.listingUrl)
              });
            }
            return processed;
          })
          .filter((result): result is ApplicationResult => result !== null);
        
        console.log('🔍 DEBUG: Processed successful results:', {
          rawCount: rawResults.length,
          processedCount: processedResults.length,
          firstResult: processedResults[0] ? {
            id: processedResults[0]._id,
            status: processedResults[0].status,
            title: processedResults[0].listingTitle
          } : null
        });
        
        // Merge/validate stats using client-side derived numbers if backend seems inconsistent
        try {
          const successes = processedResults.filter(r => r.status === 'success' || ((r as any).isSuccessful === true || (r as any).response?.success === true)).length;
          const total = processedResults.length;
          const today = new Date(); today.setHours(0,0,0,0);
          const todayCount = processedResults.filter(r => {
            const d = new Date(r.submittedAt);
            return d >= today;
          }).length;

          setStats(prev => {
            const merged = {
              ...(prev || {} as any),
              totalApplications: total || prev?.totalApplications || 0,
              successfulApplications: successes || prev?.successfulApplications || 0,
              successRate: total > 0 ? Math.round((successes / total) * 100) : (prev?.successRate || 0),
              applicationsToday: todayCount ?? prev?.applicationsToday ?? 0,
            } as AutoApplicationStats;
            console.log('🔧 Merged stats (client-derived + backend):', merged);
            return merged;
          });
        } catch (e) {
          console.warn('Failed to derive client stats:', e);
        }

        setRecentResults(processedResults);
      } else {
        console.log('🔍 DEBUG: Results API failed:', {
          status: resultsResponse.status,
          error: resultsResponse.status === 'rejected' ? resultsResponse.reason : 'fulfilled but not success',
          response: resultsResponse.status === 'fulfilled' ? resultsResponse.value : undefined
        });
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Fetch autonomous status
  useEffect(() => {
    if (user?.id) {
      fetchAutonomousStatus(true); // Force fetch to get latest status
    }
  }, [user?.id, fetchAutonomousStatus]);

  // Filter functions
  const getFilteredQueue = () => {
    if (queueFilter === 'all') return queue;
    return queue.filter(item => item.status === queueFilter);
  };

  const getFilteredResults = () => {
    if (resultsFilter === 'all') return recentResults;
    
    return recentResults.filter(result => {
      if (resultsFilter === 'submitted') {
        // For submitted filter, show successful applications
        return result.status === 'success' || 
               (result.status === 'submitted' && (
                 (result as any).isSuccessful === true ||
                 (result as any).response?.success === true
               ));
      }
      return result.status === resultsFilter;
    });
  };

  // Queue management
  const handleRemoveFromQueue = async (queueId: string) => {
    try {
      const response = await autoApplicationService.removeFromQueue(queueId);
      if (response.success) {
        setQueue(queue.filter(item => item._id !== queueId));
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', 'Item removed from queue');
      } else {
        // Handle API response errors
        const errorMessage = response.error || response.message || 'Failed to remove item from queue';
        console.error('API Error removing from queue:', response);
        Alert.alert('Error', errorMessage);
      }
    } catch (error: any) {
      // Handle network/server errors
      console.error('Network Error removing from queue:', {
        message: error.message,
        status: error.status,
        code: error.code,
        response: error.response?.data
      });
      
      let errorMessage = 'Failed to remove item from queue';
      
      if (error.status === 500) {
        // Check if this is the specific "not a function" error
        if (error.message && error.message.includes('is not a function')) {
          console.error('Backend method not implemented:', error.message);
          errorMessage = 'Queue removal feature is not yet implemented on the server. Please contact support.';
          
          // Don't remove from local state for this error type
          Alert.alert('Feature Not Available', 
            'Queue removal is temporarily unavailable. The feature is being implemented on the server.',
            [{ text: 'OK', style: 'default' }]
          );
          return; // Exit early, don't show generic error
        }
        
        // For other 500 errors, remove from local state as workaround
        console.warn('Server error - removing item locally as workaround');
        setQueue(queue.filter(item => item._id !== queueId));
        errorMessage = 'Server error occurred. Item removed locally. Please refresh to sync with server.';
        
        // Still refresh to sync state
        console.log('Refreshing queue after server error...');
        setTimeout(() => loadData(true), 1000);
      } else if (error.status === 404) {
        errorMessage = 'Queue item not found. It may have already been processed.';
        // Remove from local state since it doesn't exist on server
        setQueue(queue.filter(item => item._id !== queueId));
      } else if (error.status === 0) {
        errorMessage = 'Network connection error. Please check your internet connection.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Alert.alert('Error', errorMessage);
    }
  };

  const handleUpdatePriority = async (queueId: string, priority: number) => {
    try {
      const response = await autoApplicationService.updateQueuePriority(queueId, priority);
      if (response.success && response.data) {
        setQueue(queue.map(item =>
          item._id === queueId ? { ...item, priority } : item
        ));
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('Error updating priority:', error);
      Alert.alert('Error', 'Failed to update priority');
    }
  };

  const handlePauseQueue = async () => {
    if (!user?.id || pausingQueue) return;

    try {
      setPausingQueue(true);
      const response = await autoApplicationService.pauseQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue paused';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to pause queue');
      }
    } catch (error: any) {
      console.error('Error pausing queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to pause queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setPausingQueue(false);
    }
  };

  const handleResumeQueue = async () => {
    if (!user?.id || resumingQueue) return;

    try {
      setResumingQueue(true);
      const response = await autoApplicationService.resumeQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue resumed';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to resume queue');
      }
    } catch (error: any) {
      console.error('Error resuming queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to resume queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setResumingQueue(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      {/* Tab Navigation */}
      <Animated.View
        style={styles.tabContainer}
        entering={FadeInUp.duration(600).delay(200)}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContent}
        >
          {[
            { key: 'overview', label: 'Overview', icon: 'analytics-outline' },
            { key: 'queue', label: 'Queue', icon: 'list-outline' },
            { key: 'results', label: 'Results', icon: 'checkmark-done-outline' },
            { key: 'autonomous', label: 'Autonomous', icon: 'flash-outline' },
          ].map((t) => (
            <TouchableOpacity
              key={t.key}
              style={[styles.tab, activeTab === t.key && styles.activeTab]}
              onPress={() => {
                setActiveTab(t.key as any);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              activeOpacity={0.9}
            >
              <Ionicons
                name={t.icon as any}
                size={18}
                color={activeTab === t.key ? THEME.primary : THEME.gray}
              />
              <Text
                numberOfLines={1}
                style={[styles.tabText, activeTab === t.key && styles.activeTabText]}
              >
                {t.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Animated.View>

      {activeTab === 'autonomous' ? (
        <AutonomousStatusDashboard />
      ) : (
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadData(true)}
              colors={[THEME.primary]}
            />
          }
        >
        {activeTab === 'overview' && (
          <>
            {/* Stats Overview */}
            <Animated.View
              style={styles.statsContainer}
              entering={FadeInUp.duration(600).delay(400)}
            >
              <Text style={styles.sectionTitle}>Statistics</Text>
              <View style={styles.statsGrid}>
                <StatsCard
                  title="Total Applications"
                  value={stats?.totalApplications || 0}
                  icon="paper-plane-outline"
                  color={THEME.primary}
                />
                <StatsCard
                  title="Success Rate"
                  value={`${stats?.successRate || 0}%`}
                  icon="checkmark-circle-outline"
                  color={THEME.success}
                />
                <StatsCard
                  title="Today"
                  value={stats?.applicationsToday || 0}
                  subtitle="applications"
                  icon="today-outline"
                  color={THEME.accent}
                />
                <StatsCard
                  title="Pending Queue"
                  value={queue.filter(item => item.status === 'pending').length}
                  subtitle="waiting"
                  icon="hourglass-outline"
                  color={THEME.warning}
                />
              </View>
            </Animated.View>


            {/* Quick Actions */}
            <Animated.View
              style={styles.quickActionsContainer}
              entering={FadeInUp.duration(600).delay(600)}
            >
              <Text style={styles.sectionTitle}>Quick Actions</Text>
              <View style={styles.quickActionsGrid}>
                <TouchableOpacity
                  style={[styles.quickAction, pausingQueue && styles.quickActionDisabled]}
                  onPress={handlePauseQueue}
                  disabled={pausingQueue}
                >
                  {pausingQueue ? (
                    <ActivityIndicator size="small" color={THEME.warning} />
                  ) : (
                    <Ionicons name="pause-circle-outline" size={24} color={THEME.warning} />
                  )}
                  <Text style={styles.quickActionText}>
                    {pausingQueue ? 'Pausing...' : 'Pause Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.quickAction, resumingQueue && styles.quickActionDisabled]}
                  onPress={handleResumeQueue}
                  disabled={resumingQueue}
                >
                  {resumingQueue ? (
                    <ActivityIndicator size="small" color={THEME.success} />
                  ) : (
                    <Ionicons name="play-circle-outline" size={24} color={THEME.success} />
                  )}
                  <Text style={styles.quickActionText}>
                    {resumingQueue ? 'Resuming...' : 'Resume Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => router.push('/auto-application-settings')}
                >
                  <Ionicons name="settings-outline" size={24} color={THEME.primary} />
                  <Text style={styles.quickActionText}>Settings</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => loadData()}
                >
                  <Ionicons name="refresh-outline" size={24} color={THEME.gray} />
                  <Text style={styles.quickActionText}>Refresh</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </>
        )}

        {activeTab === 'queue' && (
          <Animated.View
            style={styles.queueContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.queueHeader}>
              <Text style={styles.sectionTitle}>Application Queue ({getFilteredQueue().length})</Text>
              <TouchableOpacity
                style={styles.queueAction}
                onPress={() => loadData()}
              >
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </TouchableOpacity>
            </View>

            {/* Queue Filter */}
            <FilterPicker
              label="Filter by Status"
              options={[
                { label: 'All', value: 'all' },
                { label: 'Pending', value: 'pending' },
                { label: 'Processing', value: 'processing' },
                { label: 'Paused', value: 'paused' },
                { label: 'Retrying', value: 'retrying' },
                { label: 'Completed', value: 'completed' },
                { label: 'Failed', value: 'failed' },
                { label: 'Cancelled', value: 'cancelled' },
              ]}
              selectedValue={queueFilter}
              onValueChange={setQueueFilter}
            />

            {getFilteredQueue().length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="list-outline" size={48} color={THEME.gray} />
                <Text style={styles.emptyStateText}>
                  {queueFilter === 'all' 
                    ? 'No applications in queue' 
                    : `No ${queueFilter} applications found`}
                </Text>
                <Text style={styles.emptyStateSubtext}>
                  {queueFilter === 'all'
                    ? 'Applications will appear here when they match your criteria'
                    : `Try changing the filter to see other application statuses`}
                </Text>
              </View>
            ) : (
              <View style={styles.queueList}>
                {getFilteredQueue().map((item) => (
                  <QueueItem
                    key={item._id}
                    item={item}
                    onRemove={handleRemoveFromQueue}
                    onUpdatePriority={handleUpdatePriority}
                  />
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {activeTab === 'results' && (
          <Animated.View
            style={styles.resultsContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.resultsHeader}>
              <Text style={styles.sectionTitle}>
                {resultsFilter === 'submitted' ? 'Successful Applications' : 
                 resultsFilter === 'all' ? 'All Results' : 
                 `${resultsFilter.charAt(0).toUpperCase() + resultsFilter.slice(1)} Applications`} ({getFilteredResults().length})
              </Text>
              <TouchableOpacity
                style={styles.queueAction}
                onPress={() => loadData(true)}
              >
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </TouchableOpacity>
            </View>

            {/* Results Filter */}
            <FilterPicker
              label="Filter by Status"
              options={[
                { label: 'All Results', value: 'all' },
                { label: 'Submitted', value: 'submitted' },
                { label: 'Failed', value: 'failed' },
                { label: 'Cancelled', value: 'cancelled' },
              ]}
              selectedValue={resultsFilter}
              onValueChange={setResultsFilter}
            />

            {/* Email Check Message - Show when there are successful applications and submitted filter is active */}
            {getFilteredResults().length > 0 && resultsFilter === 'submitted' && (
              <Animated.View 
                style={styles.emailCheckNotice}
                entering={FadeInUp.duration(400).delay(200)}
              >
                <View style={styles.emailNoticeHeader}>
                  <LinearGradient
                    colors={[THEME.primary, THEME.secondary]}
                    style={styles.emailNoticeIcon}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Ionicons name="mail" size={20} color={THEME.light} />
                  </LinearGradient>
                  <View style={styles.emailNoticeContent}>
                    <Text style={styles.emailNoticeTitle}>Check Your Email</Text>
                    <Text style={styles.emailNoticeText}>
                      Your applications have been successfully submitted! Check your inbox for landlord responses and next steps.
                    </Text>
                  </View>
                </View>
              </Animated.View>
            )}

            {getFilteredResults().length === 0 ? (
              <Animated.View 
                style={styles.enhancedEmptyState}
                entering={FadeInUp.duration(600)}
              >
                <LinearGradient
                  colors={[`${THEME.primary}15`, `${THEME.secondary}15`]}
                  style={styles.emptyStateIconContainer}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Ionicons name="document-text-outline" size={48} color={THEME.primary} />
                </LinearGradient>
                <Text style={styles.enhancedEmptyStateTitle}>
                  {resultsFilter === 'submitted' ? 'No Successful Applications Yet' : 
                   resultsFilter === 'all' ? 'No Results Yet' : 
                   `No ${resultsFilter.charAt(0).toUpperCase() + resultsFilter.slice(1)} Applications`}
                </Text>
                <Text style={styles.enhancedEmptyStateText}>
                  {resultsFilter === 'submitted' 
                    ? 'Your successful applications will appear here. Once you have successful applications, check your email for landlord responses and next steps.' 
                    : `No ${resultsFilter === 'all' ? 'application results' : resultsFilter + ' applications'} found. Try changing the filter or check back later.`}
                  {resultsFilter === 'submitted' && (autonomousStatus.isActive 
                    ? ' Autonomous mode is active and monitoring for new listings automatically.' 
                    : ' Enable autonomous mode to begin automatic applications.')}
                </Text>
                <View style={styles.emptyStateActions}>
                  <TouchableOpacity
                    style={[
                      styles.primaryActionButton,
                      autonomousStatus.isActive && styles.disabledButton
                    ]}
                    onPress={() => setActiveTab('autonomous')}
                    disabled={autonomousStatus.isActive}
                  >
                    <Ionicons 
                      name={autonomousStatus.isActive ? "checkmark-circle" : "flash"} 
                      size={18} 
                      color={autonomousStatus.isActive ? THEME.gray : THEME.light} 
                    />
                    <Text style={[
                      styles.primaryActionText,
                      autonomousStatus.isActive && styles.disabledButtonText
                    ]}>
                      {autonomousStatus.isActive ? 'Auto Mode Active' : 'Enable Auto Mode'}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.secondaryActionButton}
                    onPress={() => loadData(true)}
                  >
                    <Ionicons name="refresh-outline" size={16} color={THEME.primary} />
                    <Text style={styles.secondaryActionText}>Refresh</Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            ) : (
              <View style={styles.resultsList}>
                {getFilteredResults().map((result, index) => {
                  // Validate result data before rendering
                  if (!result || !result._id) {
                    console.warn(`Invalid result at index ${index}:`, result);
                    return (
                      <View key={`invalid-${index}`} style={styles.enhancedResultItem}>
                        <View style={styles.enhancedErrorContainer}>
                          <View style={styles.errorHeader}>
                            <Ionicons name="alert-circle" size={16} color={THEME.danger} />
                            <Text style={styles.errorHeaderText}>Invalid Data</Text>
                          </View>
                          <Text style={styles.enhancedErrorText}>Unable to display result due to invalid data</Text>
                        </View>
                      </View>
                    );
                  }
                  
                  try {
                    return (
                      <Animated.View
                        key={result._id}
                        entering={FadeInUp.duration(400).delay(index * 100)}
                      >
                        <RecentResult result={result} />
                      </Animated.View>
                    );
                  } catch (error) {
                    console.error(`Error rendering result ${result._id}:`, error);
                    return (
                      <View key={`error-${result._id}`} style={styles.enhancedResultItem}>
                        <View style={styles.enhancedErrorContainer}>
                          <View style={styles.errorHeader}>
                            <Ionicons name="alert-circle" size={16} color={THEME.danger} />
                            <Text style={styles.errorHeaderText}>Render Error</Text>
                          </View>
                          <Text style={styles.enhancedErrorText}>Unable to display this result</Text>
                        </View>
                      </View>
                    );
                  }
                })}
              </View>
            )}
          </Animated.View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    paddingVertical: 6,
    paddingHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
  },
  tabsScrollContent: {
    paddingHorizontal: 4,
    gap: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 18,
    gap: 6,
    borderWidth: 1,
    borderColor: 'transparent',
    minWidth: 110,
  },
  activeTab: {
    backgroundColor: 'rgba(67,97,238,0.12)',
    borderColor: THEME.primary,
  },
  tabText: {
    fontSize: 13,
    fontWeight: '600',
    color: THEME.gray,
  },
  activeTabText: {
    color: THEME.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statsIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statsContent: {
    flex: 1,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 4,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  statsSubtitle: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: 2,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickActionsContainer: {
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickAction: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginTop: 8,
  },
  quickActionDisabled: {
    opacity: 0.6,
  },
  queueContainer: {
    marginBottom: 24,
  },
  queueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  queueAction: {
    padding: 8,
  },
  queueList: {
    gap: 12,
  },
  queueItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  queueItemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  queueItemTitle: {
    flex: 1,
    marginRight: 12,
  },
  queueItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  queueItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#ffffff',
  },
  priorityText: {
    fontSize: 12,
    color: THEME.gray,
  },
  queueItemActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: THEME.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButton: {
    backgroundColor: '#fee2e2',
  },
  queueItemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  queueItemDetail: {
    fontSize: 12,
    color: THEME.gray,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fee2e2',
    padding: 8,
    borderRadius: 6,
    gap: 6,
  },
  errorText: {
    fontSize: 12,
    color: THEME.danger,
    flex: 1,
  },
  resultsContainer: {
    marginBottom: 24,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  resultsList: {
    gap: 12,
  },
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultStatus: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  resultTime: {
    fontSize: 12,
    color: THEME.gray,
    marginBottom: 4,
  },
  resultStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: THEME.primary,
  },
  resultDetails: {
    marginTop: 8,
    gap: 4,
  },
  resultDetail: {
    fontSize: 11,
    color: THEME.gray,
  },
  resultMeta: {
    alignItems: 'flex-end',
  },
  resultScore: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.primary,
    marginBottom: 2,
  },
  resultResponseTime: {
    fontSize: 12,
    color: THEME.gray,
  },
  landlordResponse: {
    marginTop: 12,
    padding: 12,
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
  },
  landlordResponseText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginBottom: 4,
  },
  landlordResponseMessage: {
    fontSize: 12,
    color: THEME.gray,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.gray,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    paddingHorizontal: 40,
    marginBottom: 16,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
  },
  bottomSpacing: {
    height: 40,
  },
  
  // Enhanced Result Item Styles
  enhancedResultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.06)',
  },
  
  enhancedResultHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  
  statusIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  
  enhancedResultContent: {
    flex: 1,
    marginRight: 12,
  },
  
  enhancedResultTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.dark,
    marginBottom: 8,
    lineHeight: 22,
  },
  
  resultStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    flex: 1,
  },
  
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  
  enhancedResultTime: {
    fontSize: 11,
    color: THEME.gray,
    fontWeight: '500',
    marginLeft: 4,
  },
  
  scoreBadgeContainer: {
    alignItems: 'center',
    backgroundColor: `${THEME.primary}10`,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: `${THEME.primary}20`,
    minWidth: 60,
  },
  
  scoreBadgeText: {
    fontSize: 18,
    fontWeight: '800',
    color: THEME.primary,
    lineHeight: 20,
  },
  
  scoreBadgeLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: THEME.gray,
    marginTop: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  
  metricsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    flexWrap: 'wrap',
    gap: 16,
  },
  
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    minWidth: 100,
  },
  
  metricText: {
    fontSize: 12,
    color: THEME.gray,
    fontWeight: '500',
    marginLeft: 6,
    textTransform: 'capitalize',
  },
  
  enhancedErrorContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: THEME.danger,
    marginBottom: 12,
  },
  
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  
  errorHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.danger,
    marginLeft: 8,
  },
  
  enhancedErrorText: {
    fontSize: 13,
    color: '#7f1d1d',
    lineHeight: 18,
    marginBottom: 8,
  },
  
  errorTag: {
    backgroundColor: THEME.danger,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginTop: 4,
    marginRight: 8,
  },
  
  errorTagText: {
    fontSize: 10,
    fontWeight: '600',
    color: THEME.light,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  
  
  // Enhanced Empty State
  enhancedEmptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  
  emptyStateIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: THEME.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  
  enhancedEmptyStateTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: THEME.dark,
    marginBottom: 12,
    textAlign: 'center',
  },
  
  enhancedEmptyStateText: {
    fontSize: 16,
    color: THEME.gray,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 280,
  },
  
  emptyStateActions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
    maxWidth: 300,
  },
  
  primaryActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.primary,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
    flex: 1,
    marginRight: 8,
  },

  primaryActionText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: '600',
  },

  disabledButton: {
    backgroundColor: THEME.lightGray,
    borderWidth: 1,
    borderColor: THEME.gray + '40',
  },

  disabledButtonText: {
    color: THEME.gray,
  },
  
  secondaryActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.lightGray,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: `${THEME.primary}30`,
    gap: 8,
  },
  
  secondaryActionText: {
    color: THEME.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Check Email Section
  checkEmailContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: THEME.primary,
  },
  
  emailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  
  emailHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.primary,
    marginLeft: 8,
    flex: 1,
  },
  
  successBadge: {
    backgroundColor: THEME.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  
  successBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: THEME.light,
    letterSpacing: 0.5,
  },
  
  emailInstructionText: {
    fontSize: 14,
    color: '#1e40af',
    lineHeight: 20,
    marginBottom: 12,
  },
  
  emailTips: {
    gap: 8,
  },
  
  emailTip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  
  emailTipText: {
    fontSize: 12,
    color: THEME.gray,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  
  openEmailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    marginTop: 12,
    borderWidth: 1,
    borderColor: `${THEME.primary}30`,
    gap: 8,
  },
  
  openEmailText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },

  // Email Check Notice Styles
  emailCheckNotice: {
    backgroundColor: '#f0f9ff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: `${THEME.primary}20`,
    shadowColor: THEME.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  emailNoticeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },

  emailNoticeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },

  emailNoticeContent: {
    flex: 1,
  },

  emailNoticeTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.primary,
    marginBottom: 4,
  },

  emailNoticeText: {
    fontSize: 14,
    color: '#1e40af',
    lineHeight: 20,
    fontWeight: '500',
  },

  // Filter Styles
  filterContainer: {
    marginBottom: 16,
  },

  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },

  filterOptions: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 2,
  },

  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: THEME.lightGray,
    borderWidth: 1,
    borderColor: 'transparent',
  },

  filterOptionActive: {
    backgroundColor: `${THEME.primary}15`,
    borderColor: THEME.primary,
  },

  filterOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },

  filterOptionTextActive: {
    color: THEME.primary,
    fontWeight: '600',
  },
});

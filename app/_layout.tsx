import {
  DarkTheme,
  De<PERSON>ult<PERSON>heme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack, useRouter, useSegments } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { SafeAreaProvider } from "react-native-safe-area-context";

import AppInitializer from "@/components/AppInitializer";
import ErrorBoundary from "@/components/ErrorBoundary";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useAuthStore } from "@/store/authStore";
import { debugLogger } from "../services/debugLogger";

import { QueryProvider } from "@/components/QueryProvider";

// Initialize i18n
import "@/i18n";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  const segments = useSegments();
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();

  // Handle automatic navigation based on auth state
  useEffect(() => {
    const handleNavigation = async () => {
      // Get current route - if segments is empty, we're on the index route
      const currentRoute = segments.length > 0 ? segments[0] : "index";
      const protectedRoutes = [
        "dashboard",
        "profile",
        "application",
        "contract-review",
        "listing-details",
        "property-owner",
      ];
      const authRoutes = ["login"];

      console.log('🔍 DEBUG: Navigation check - currentRoute:', currentRoute, 'segments:', segments, 'isAuthenticated:', isAuthenticated);

      // Helper function to check if preferences are valid and complete
      const hasValidPreferences = (preferences: any) => {
        if (!preferences) return false;

        // Check if preferences object exists and has required fields
        const requiredFields = [
          "minPrice",
          "maxPrice",
          "preferredLocations",
          "propertyTypes",
          "minRooms",
          "maxRooms",
          "amenities",
          "notifications",
        ];

        // Check if all required fields exist and have valid values
        const hasAllFields = requiredFields.every((field) => {
          const value = preferences[field];
          if (value === undefined || value === null) return false;

          // Check arrays have at least one item
          if (Array.isArray(value) && field !== "amenities") {
            return value.length > 0;
          }

          // Check notifications object has required fields
          if (field === "notifications") {
            return (
              typeof value === "object" &&
              "email" in value &&
              "push" in value &&
              "sms" in value
            );
          }

          return true;
        });

        return hasAllFields;
      };

      // Early out if AppInitializer already navigated to the target to avoid flicker
      if ((globalThis as any).__BOOT_NAV_DONE__) {
        return;
      }

      // If the user is authenticated but doesn't have valid preferences, redirect to preferences
      // BUT skip this check for property owners who don't need tenant preferences
      if (isAuthenticated && user) {
        // Check if user is a property owner
        const isPropertyOwner =
          user.role === "owner" ||
          (user.propertyOwner && user.propertyOwner.isPropertyOwner);

        // Authenticated; minimal logging retained via debugLogger if needed

        // If user is a property owner, ensure they're on the property owner routes
        if (isPropertyOwner) {
          if (
            currentRoute !== "property-owner" &&
            !currentRoute.startsWith("property-owner")
          ) {
            debugLogger.log(
              "LAYOUT_GUARD",
              "Routing property owner to their dashboard"
            );
            router.replace("/property-owner/dashboard");
            return;
          }
        } else {
          // Only check preferences for tenants, not property owners
          const hasPreferences = hasValidPreferences(user.preferences);

          if (
            !hasPreferences &&
            currentRoute !== "preferences" &&
            !authRoutes.includes(currentRoute)
          ) {
            debugLogger.log(
              "LAYOUT_GUARD",
              "User has no preferences, checking redirect blocker",
              { currentRoute }
            );

            // Check redirect blocker service
            try {
              const { redirectBlockerService } = await import(
                "../services/redirectBlockerService"
              );

              const areBlocked = redirectBlockerService.areRedirectsBlocked();
              debugLogger.log("LAYOUT_GUARD", "Checked redirect blocker", {
                areBlocked,
              });

              if (areBlocked) {
                debugLogger.log(
                  "LAYOUT_GUARD",
                  "Redirects blocked, staying on current screen"
                );
                return;
              }
            } catch (error) {
              console.error("Error checking redirect blocker:", error);
              debugLogger.log(
                "LAYOUT_GUARD",
                "Error checking redirect blocker",
                { error: error instanceof Error ? error.message : String(error) }
              );
            }

            debugLogger.log(
              "LAYOUT_GUARD",
              "Routing to preferences (no valid preferences)"
            );
            debugLogger.log(
              "LAYOUT_GUARD",
              "Calling router.replace(/preferences)"
            );
            router.replace("/preferences");
            debugLogger.log(
              "LAYOUT_GUARD",
              "router.replace(/preferences) completed"
            );
          }
        }
      } else if (!isAuthenticated) {
        // If the user is not authenticated, check welcome screen status
        console.log("🔍 DEBUG: User not authenticated, checking welcome screen status");

        try {
          const { welcomeService } = await import(
            "../services/welcomeService"
          );
          const hasSeenWelcome = await welcomeService.hasSeenWelcome();
          console.log("🔍 DEBUG: hasSeenWelcome:", hasSeenWelcome);

          if (hasSeenWelcome) {
            // User has seen welcome screen, redirect to login unless already there
            if (currentRoute !== "login") {
              console.log("✅ DEBUG: User has seen welcome screen, redirecting to login");
              router.replace("/login");
            } else {
              console.log("🔍 DEBUG: Already on login screen, no redirect needed");
            }
          } else {
            // User hasn't seen welcome screen, redirect to welcome unless already there
            if (currentRoute !== "index") {
              console.log("✅ DEBUG: User has not seen welcome screen, redirecting to welcome");
              router.replace("/");
            } else {
              console.log("🔍 DEBUG: Already on welcome screen, no redirect needed");
            }
          }
        } catch (error) {
          console.error("❌ DEBUG: Error checking welcome status:", error);
          // Fallback to welcome screen on error
          if (currentRoute !== "index") {
            console.log("🔄 DEBUG: Fallback - redirecting to welcome screen");
            router.replace("/");
          }
        }
      }
    };

    // Add a delay to ensure AppInitializer has finished and prevent rapid navigation changes
    const timeoutId = setTimeout(handleNavigation, 100);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticated, segments, user, router]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <SafeAreaProvider>
      <ErrorBoundary>
        <QueryProvider>
          <AppInitializer>
            <ThemeProvider
              value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
            >
              <Stack
                screenOptions={{
                  animation: "slide_from_right",
                  gestureEnabled: true,
                }}
              >
                <Stack.Screen name="index" options={{ headerShown: false }} />
                <Stack.Screen name="login" options={{ headerShown: false }} />
                <Stack.Screen
                  name="preferences"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="dashboard"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="listing-details"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="application"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="contract-review"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="profile" options={{ headerShown: false }} />
                <Stack.Screen
                  name="property-owner"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="autonomous-settings"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="autonomous-status"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="safety-controls"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="auto-application-dashboard"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="auto-application-settings"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="notification-settings"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="notification-history"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="edit-profile"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="change-password"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="settings"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </AppInitializer>
        </QueryProvider>
      </ErrorBoundary>
    </SafeAreaProvider>
  );
}

import i18n from '@/i18n';

/**
 * Utility functions for managing translations
 */

/**
 * Add new translation keys to existing resources
 * @param language - Language code (e.g., 'en', 'nl')
 * @param namespace - Namespace for the translations (e.g., 'common', 'auth')
 * @param translations - Object containing the new translations
 */
export const addTranslations = (
  language: string,
  namespace: string,
  translations: Record<string, any>
) => {
  i18n.addResourceBundle(language, 'translation', {
    [namespace]: translations,
  }, true, true);
};

/**
 * Get current language
 */
export const getCurrentLanguage = (): string => {
  return i18n.language;
};

/**
 * Check if a translation key exists
 * @param key - Translation key to check
 * @param language - Optional language code, defaults to current language
 */
export const translationExists = (key: string, language?: string): boolean => {
  return i18n.exists(key, { lng: language });
};

/**
 * Get all available languages
 */
export const getAvailableLanguages = (): string[] => {
  return Object.keys(i18n.options.resources || {});
};

/**
 * Format currency based on current language
 * @param amount - Amount to format
 * @param currency - Currency code (default: EUR)
 */
export const formatCurrency = (amount: number, currency: string = 'EUR'): string => {
  const language = getCurrentLanguage();
  const locale = language === 'nl' ? 'nl-NL' : 'en-US';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

/**
 * Format date based on current language
 * @param date - Date to format
 * @param options - Intl.DateTimeFormatOptions
 */
export const formatDate = (
  date: Date,
  options?: Intl.DateTimeFormatOptions
): string => {
  const language = getCurrentLanguage();
  const locale = language === 'nl' ? 'nl-NL' : 'en-US';
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return new Intl.DateTimeFormat(locale, options || defaultOptions).format(date);
};

/**
 * Pluralization helper for complex plural forms
 * @param count - Number for pluralization
 * @param key - Base translation key
 */
export const pluralize = (count: number, key: string): string => {
  return i18n.t(key, { count });
};

export default {
  addTranslations,
  getCurrentLanguage,
  translationExists,
  getAvailableLanguages,
  formatCurrency,
  formatDate,
  pluralize,
};